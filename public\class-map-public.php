<?php
/**
 * Public functionality class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Public class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Public {

    /**
     * Frontend widget instance
     *
     * @var MAP_Frontend_Widget
     * @since 1.0.0
     */
    public $widget;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->init();
    }

    /**
     * Initialize public functionality
     *
     * @since 1.0.0
     */
    private function init() {
        // Initialize frontend widget
        $this->widget = new MAP_Frontend_Widget();
        
        // Add hooks
        $this->add_hooks();
    }

    /**
     * Add WordPress hooks
     *
     * @since 1.0.0
     */
    private function add_hooks() {
        // Add body classes
        add_filter('body_class', array($this, 'add_body_classes'));
        
        // Add meta tags for accessibility
        add_action('wp_head', array($this, 'add_accessibility_meta'));
        
        // Add structured data
        add_action('wp_head', array($this, 'add_structured_data'));
        
        // Modify content for better accessibility
        add_filter('the_content', array($this, 'enhance_content_accessibility'));
        
        // Add keyboard navigation support
        add_action('wp_footer', array($this, 'add_keyboard_navigation'));
    }

    /**
     * Add body classes for styling
     *
     * @param array $classes
     * @return array
     * @since 1.0.0
     */
    public function add_body_classes($classes) {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if ($settings['text_to_speech_enabled']) {
            $classes[] = 'map-tts-enabled';
            $classes[] = 'map-widget-' . str_replace('_', '-', $settings['widget_position']);
            $classes[] = 'map-style-' . $settings['widget_style'];
        }
        
        return $classes;
    }

    /**
     * Add accessibility meta tags
     *
     * @since 1.0.0
     */
    public function add_accessibility_meta() {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        echo '<meta name="accessibility-features" content="text-to-speech">' . "\n";
        echo '<meta name="accessibility-plugin" content="My Accessibility Plugin v' . MAP_VERSION . '">' . "\n";
    }

    /**
     * Add structured data for accessibility
     *
     * @since 1.0.0
     */
    public function add_structured_data() {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'accessibilityFeature' => array(
                'textToSpeech',
                'keyboardNavigation'
            ),
            'accessibilityAPI' => 'WebSpeechAPI'
        );
        
        echo '<script type="application/ld+json">' . json_encode($structured_data) . '</script>' . "\n";
    }

    /**
     * Enhance content accessibility
     *
     * @param string $content
     * @return string
     * @since 1.0.0
     */
    public function enhance_content_accessibility($content) {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled'] || is_admin()) {
            return $content;
        }
        
        // Add ARIA labels to headings
        $content = preg_replace_callback(
            '/<(h[1-6])([^>]*)>(.*?)<\/h[1-6]>/i',
            function($matches) {
                $tag = $matches[1];
                $attributes = $matches[2];
                $text = $matches[3];
                
                // Add aria-label if not present
                if (strpos($attributes, 'aria-label') === false) {
                    $level = substr($tag, 1);
                    $attributes .= ' aria-label="Heading level ' . $level . ': ' . strip_tags($text) . '"';
                }
                
                return '<' . $tag . $attributes . '>' . $text . '</' . $tag . '>';
            },
            $content
        );
        
        // Add skip links for better navigation
        if (is_singular()) {
            $skip_links = '<div class="map-skip-links" aria-label="Skip navigation links">';
            $skip_links .= '<a href="#map-main-content" class="map-skip-link">' . __('Skip to main content', MAP_TEXT_DOMAIN) . '</a>';
            $skip_links .= '<a href="#map-speech-widget" class="map-skip-link">' . __('Skip to accessibility tools', MAP_TEXT_DOMAIN) . '</a>';
            $skip_links .= '</div>';
            
            $content = $skip_links . '<div id="map-main-content">' . $content . '</div>';
        }
        
        return $content;
    }

    /**
     * Add keyboard navigation support
     *
     * @since 1.0.0
     */
    public function add_keyboard_navigation() {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled'] || !$settings['keyboard_shortcuts']) {
            return;
        }
        
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Keyboard shortcuts for accessibility
            document.addEventListener('keydown', function(e) {
                // Alt + A: Activate accessibility widget
                if (e.altKey && e.key === 'a') {
                    e.preventDefault();
                    var widget = document.getElementById('map-speech-button');
                    if (widget) {
                        widget.click();
                        widget.focus();
                    }
                }
                
                // Alt + S: Start/Stop speech
                if (e.altKey && e.key === 's') {
                    e.preventDefault();
                    var playPause = document.getElementById('map-play-pause');
                    if (playPause && playPause.style.display !== 'none') {
                        playPause.click();
                    }
                }
                
                // Alt + X: Stop speech
                if (e.altKey && e.key === 'x') {
                    e.preventDefault();
                    var stop = document.getElementById('map-stop');
                    if (stop && stop.style.display !== 'none') {
                        stop.click();
                    }
                }
                
                // Escape: Close accessibility controls
                if (e.key === 'Escape') {
                    var controls = document.getElementById('map-speech-controls');
                    if (controls && controls.style.display !== 'none') {
                        controls.style.display = 'none';
                        document.getElementById('map-speech-button').focus();
                    }
                }
            });
            
            // Add focus indicators
            var focusableElements = document.querySelectorAll('.map-speech-widget button, .map-speech-widget input');
            focusableElements.forEach(function(element) {
                element.addEventListener('focus', function() {
                    this.classList.add('map-focused');
                });
                
                element.addEventListener('blur', function() {
                    this.classList.remove('map-focused');
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Check if current page should have accessibility features
     *
     * @return bool
     * @since 1.0.0
     */
    public function should_load_accessibility() {
        $settings = MAP_Core::get_instance()->get_settings();
        
        if (!$settings['text_to_speech_enabled']) {
            return false;
        }
        
        // Don't load on admin pages
        if (is_admin()) {
            return false;
        }
        
        // Don't load on login/register pages
        if (in_array($GLOBALS['pagenow'], array('wp-login.php', 'wp-register.php'))) {
            return false;
        }
        
        // Allow filtering
        return apply_filters('map_should_load_accessibility', true);
    }

    /**
     * Get page content for accessibility processing
     *
     * @return array
     * @since 1.0.0
     */
    public function get_page_accessibility_data() {
        global $post;
        
        $data = array(
            'page_title' => wp_get_document_title(),
            'content_type' => 'page',
            'has_content' => false,
            'language' => get_locale()
        );
        
        if (is_singular() && $post) {
            $data['content_type'] = $post->post_type;
            $data['has_content'] = !empty($post->post_content);
            $data['post_id'] = $post->ID;
        }
        
        return apply_filters('map_page_accessibility_data', $data);
    }
}
