/**
 * Admin JavaScript for My Accessibility Plugin
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Admin functionality class
     */
    class MapAdmin {
        constructor() {
            this.init();
        }

        /**
         * Initialize admin functionality
         */
        init() {
            this.bindEvents();
            this.initRangeSliders();
            this.initColorPickers();
            this.initFormValidation();
        }

        /**
         * Bind event handlers
         */
        bindEvents() {
            const self = this;

            // Range slider updates
            $(document).on('input', '.map-range-slider', function() {
                const $slider = $(this);
                const $valueDisplay = $slider.siblings('.map-range-value');
                $valueDisplay.text($slider.val());
            });

            // Form submission with AJAX
            $('#submit').on('click', function(e) {
                e.preventDefault();
                self.saveSettings();
            });

            // Reset settings confirmation
            $('input[name="map_reset_settings"]').on('click', function(e) {
                if (!confirm(mapAdmin.strings.confirmReset || 'Are you sure you want to reset all settings to defaults?')) {
                    e.preventDefault();
                }
            });

            // Import file validation
            $('input[name="settings_file"]').on('change', function() {
                const file = this.files[0];
                if (file && !file.name.endsWith('.json')) {
                    alert('Please select a valid JSON file.');
                    $(this).val('');
                }
            });

            // Preview functionality
            $(document).on('click', '.map-preview-button', function(e) {
                e.preventDefault();
                self.previewSettings();
            });

            // Live preview updates
            $(document).on('change', 'input, select', function() {
                self.updateLivePreview();
            });
        }

        /**
         * Initialize range sliders
         */
        initRangeSliders() {
            $('.map-range-slider').each(function() {
                const $slider = $(this);
                const $valueDisplay = $slider.siblings('.map-range-value');
                
                // Set initial value
                $valueDisplay.text($slider.val());
                
                // Add step indicators if needed
                const min = parseFloat($slider.attr('min'));
                const max = parseFloat($slider.attr('max'));
                const step = parseFloat($slider.attr('step'));
                
                // Create visual indicators for common values
                if ($slider.attr('id') === 'speech_rate') {
                    this.addSliderMarkers($slider, [0.5, 1.0, 1.5, 2.0]);
                }
            });
        }

        /**
         * Add markers to slider
         */
        addSliderMarkers($slider, values) {
            const min = parseFloat($slider.attr('min'));
            const max = parseFloat($slider.attr('max'));
            const $container = $('<div class="map-slider-markers"></div>');
            
            values.forEach(value => {
                const percentage = ((value - min) / (max - min)) * 100;
                const $marker = $('<div class="map-slider-marker"></div>');
                $marker.css('left', percentage + '%');
                $marker.attr('data-value', value);
                $container.append($marker);
            });
            
            $slider.after($container);
        }

        /**
         * Initialize color pickers
         */
        initColorPickers() {
            // Modern browsers have native color picker support
            $('input[type="color"]').each(function() {
                const $input = $(this);
                
                // Add color preview
                const $preview = $('<div class="map-color-preview"></div>');
                $preview.css('background-color', $input.val());
                $input.after($preview);
                
                // Update preview on change
                $input.on('input change', function() {
                    $preview.css('background-color', $(this).val());
                });
            });
        }

        /**
         * Initialize form validation
         */
        initFormValidation() {
            const self = this;

            // Real-time validation
            $('form input, form select').on('blur', function() {
                self.validateField($(this));
            });

            // Form submission validation
            $('form').on('submit', function(e) {
                if (!self.validateForm($(this))) {
                    e.preventDefault();
                }
            });
        }

        /**
         * Validate individual field
         */
        validateField($field) {
            const fieldName = $field.attr('name');
            const value = $field.val();
            let isValid = true;
            let errorMessage = '';

            // Remove existing error styling
            $field.removeClass('map-field-error');
            $field.siblings('.map-error-message').remove();

            // Validation rules
            switch (fieldName) {
                case 'map_settings[speech_rate]':
                    const rate = parseFloat(value);
                    if (isNaN(rate) || rate < 0.1 || rate > 10) {
                        isValid = false;
                        errorMessage = 'Speech rate must be between 0.1 and 10';
                    }
                    break;

                case 'map_settings[speech_pitch]':
                    const pitch = parseFloat(value);
                    if (isNaN(pitch) || pitch < 0 || pitch > 2) {
                        isValid = false;
                        errorMessage = 'Speech pitch must be between 0 and 2';
                    }
                    break;

                case 'map_settings[speech_volume]':
                    const volume = parseFloat(value);
                    if (isNaN(volume) || volume < 0 || volume > 1) {
                        isValid = false;
                        errorMessage = 'Speech volume must be between 0 and 1';
                    }
                    break;

                case 'map_settings[button_text]':
                    if (!value.trim()) {
                        isValid = false;
                        errorMessage = 'Button text cannot be empty';
                    }
                    break;
            }

            // Show error if validation failed
            if (!isValid) {
                $field.addClass('map-field-error');
                $field.after('<div class="map-error-message">' + errorMessage + '</div>');
            }

            return isValid;
        }

        /**
         * Validate entire form
         */
        validateForm($form) {
            let isValid = true;

            $form.find('input, select').each(function() {
                if (!this.validateField($(this))) {
                    isValid = false;
                }
            });

            return isValid;
        }

        /**
         * Save settings via AJAX
         */
        saveSettings() {
            const $form = $('form');
            const $submitButton = $('#submit');
            const formData = $form.serialize();

            // Show loading state
            $submitButton.prop('disabled', true).addClass('map-loading');
            
            // Add nonce
            const data = formData + '&action=map_save_settings&nonce=' + mapAdmin.nonce;

            $.ajax({
                url: mapAdmin.ajaxurl,
                type: 'POST',
                data: data,
                success: function(response) {
                    if (response.success) {
                        this.showMessage(response.data.message || mapAdmin.strings.saved, 'success');
                        
                        // Update any live previews
                        this.updateLivePreview();
                    } else {
                        this.showMessage(response.data || mapAdmin.strings.error, 'error');
                    }
                }.bind(this),
                error: function() {
                    this.showMessage(mapAdmin.strings.error, 'error');
                }.bind(this),
                complete: function() {
                    $submitButton.prop('disabled', false).removeClass('map-loading');
                }
            });
        }

        /**
         * Preview settings
         */
        previewSettings() {
            // Open a new window/tab with current settings applied
            const previewUrl = window.location.origin + '?map_preview=1';
            window.open(previewUrl, '_blank');
        }

        /**
         * Update live preview
         */
        updateLivePreview() {
            // Update color preview
            const buttonColor = $('input[name="map_settings[button_color]"]').val();
            if (buttonColor) {
                $('.map-color-preview').css('background-color', buttonColor);
                
                // Update CSS custom property if preview exists
                if ($('.map-preview-widget').length) {
                    document.documentElement.style.setProperty('--map-primary-color', buttonColor);
                }
            }

            // Update button text preview
            const buttonText = $('input[name="map_settings[button_text]"]').val();
            if (buttonText) {
                $('.map-preview-widget .map-button-text').text(buttonText);
            }

            // Update size preview
            const buttonSize = $('select[name="map_settings[button_size]"]').val();
            if (buttonSize) {
                $('.map-preview-widget')
                    .removeClass('map-size-small map-size-medium map-size-large')
                    .addClass('map-size-' + buttonSize);
            }

            // Update position preview
            const widgetPosition = $('select[name="map_settings[widget_position]"]').val();
            if (widgetPosition) {
                $('.map-preview-widget')
                    .removeClass('map-position-top-left map-position-top-right map-position-bottom-left map-position-bottom-right map-position-center')
                    .addClass('map-position-' + widgetPosition.replace('_', '-'));
            }
        }

        /**
         * Show admin message
         */
        showMessage(message, type = 'info') {
            // Remove existing messages
            $('.map-message').remove();

            // Create new message
            const $message = $('<div class="map-message ' + type + '">' + message + '</div>');
            
            // Insert after page title
            $('.wrap h1').after($message);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);

            // Scroll to message
            $('html, body').animate({
                scrollTop: $message.offset().top - 100
            }, 500);
        }

        /**
         * Initialize tooltips
         */
        initTooltips() {
            // Add tooltips to form fields
            $('[data-tooltip]').each(function() {
                const $element = $(this);
                const tooltip = $element.data('tooltip');
                
                $element.on('mouseenter focus', function() {
                    const $tooltip = $('<div class="map-tooltip">' + tooltip + '</div>');
                    $('body').append($tooltip);
                    
                    const offset = $element.offset();
                    $tooltip.css({
                        top: offset.top - $tooltip.outerHeight() - 10,
                        left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
                    });
                });
                
                $element.on('mouseleave blur', function() {
                    $('.map-tooltip').remove();
                });
            });
        }

        /**
         * Handle tab navigation
         */
        initTabs() {
            $('.map-tab-nav a').on('click', function(e) {
                e.preventDefault();
                
                const $tab = $(this);
                const target = $tab.attr('href');
                
                // Update active tab
                $('.map-tab-nav a').removeClass('nav-tab-active');
                $tab.addClass('nav-tab-active');
                
                // Show target content
                $('.map-tab-content').hide();
                $(target).show();
                
                // Update URL hash
                window.location.hash = target;
            });
            
            // Show tab based on URL hash
            if (window.location.hash) {
                $('.map-tab-nav a[href="' + window.location.hash + '"]').click();
            }
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof mapAdmin !== 'undefined') {
            new MapAdmin();
        }
    });

})(jQuery);
