/**
 * Frontend JavaScript for My Accessibility Plugin
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Main Accessibility Plugin Class
     */
    class MapAccessibility {
        constructor() {
            this.isPlaying = false;
            this.isPaused = false;
            this.isSpeechInProgress = false;
            this.currentUtterance = null;
            this.speechSynthesis = window.speechSynthesis;
            this.currentText = '';
            this.currentPosition = 0;
            this.highlightEnabled = false;
            this.autoScrollEnabled = false;
            this.isTextToSpeechActive = false;
            this.isDyslexicFontActive = false;
            this.isReadingGuideActive = false;
            this.adhdFocusMode = false; // ADHD Focus Mode state
            this.bigCursorMode = false; // Big Cursor Mode state
            this.textMagnificationMode = false; // Text Magnification Mode state
            this.currentFontSize = 100; // Default font size percentage
            this.currentLineSpacing = 1.5; // Default line spacing (1.5 = normal)
            this.currentCategory = null; // Track current category for reset functionality
            this.currentContrastTheme = 'normal'; // Default contrast theme

            // Custom theme colors
            this.customThemeColors = {
                text: '',
                background: '',
                link: '',
                heading: ''
            };
            this.isCustomThemeActive = false;
            this.customThemeDebounceTimer = null;

            // Dark mode for widget interface
            this.isDarkModeActive = false;





            // Theme selector properties
            this.themes = [
                {
                    id: 'normal',
                    name: 'Default',
                    description: 'Standard website appearance with default colors',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>'
                },
                {
                    id: 'monochrome',
                    name: 'Monochrome',
                    description: 'Grayscale colors for reduced visual distraction',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><rect x="3" y="6" width="3" height="12" fill="currentColor"/><rect x="7" y="6" width="3" height="12" fill="currentColor" opacity="0.7"/><rect x="11" y="6" width="3" height="12" fill="currentColor" opacity="0.4"/><rect x="15" y="6" width="3" height="12" fill="currentColor" opacity="0.2"/><rect x="19" y="6" width="2" height="12" fill="currentColor" opacity="0.1"/></svg>'
                },
                {
                    id: 'low-saturation',
                    name: 'Low Saturation',
                    description: 'Reduced color intensity for comfortable viewing',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><circle cx="12" cy="12" r="8" fill="none" stroke="currentColor" stroke-width="2" opacity="0.4"/><circle cx="12" cy="8" r="2" fill="currentColor" opacity="0.3"/><circle cx="8" cy="14" r="1.5" fill="currentColor" opacity="0.3"/><circle cx="16" cy="14" r="1.5" fill="currentColor" opacity="0.3"/></svg>'
                },
                {
                    id: 'high-saturation',
                    name: 'High Saturation',
                    description: 'Enhanced color intensity for vibrant viewing',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>'
                },
                {
                    id: 'dark',
                    name: 'Dark Mode',
                    description: 'Dark background with light text for low-light environments',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/></svg>'
                },
                {
                    id: 'high-contrast',
                    name: 'High Contrast',
                    description: 'Black and white for maximum readability',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18V4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/></svg>'
                },
                {
                    id: 'sepia',
                    name: 'Sepia',
                    description: 'Warm, paper-like colors for comfortable reading',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><rect x="3" y="4" width="18" height="14" rx="2" fill="none" stroke="currentColor" stroke-width="1.5"/><circle cx="8" cy="9" r="1.5" fill="currentColor"/><path d="M21 15l-3.5-3.5c-.4-.4-1-.4-1.4 0L6 21" stroke="currentColor" stroke-width="1.5" fill="none"/><rect x="2" y="2" width="20" height="20" fill="none" stroke="currentColor" stroke-width="0.5" opacity="0.3" rx="1"/></svg>'
                },
                {
                    id: 'colorblind',
                    name: 'Color Blind Friendly',
                    description: 'Optimized colors for color vision deficiency',
                    icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5z"/><circle cx="12" cy="12" r="3" fill="white"/><path d="M12 9v6M9 12h6" stroke="currentColor" stroke-width="1.5"/></svg>'
                }
            ];
            this.currentThemeIndex = 0;
            this.selectedTheme = 'normal';

            // Modal system properties
            this.currentView = 'main-menu';
            this.previousView = null;
            this.isTransitioning = false;

            this.init();
        }

        /**
         * Initialize the plugin
         */
        init() {
            if (!this.speechSynthesis) {
                return;
            }

            this.bindEvents();
            this.setupKeyboardShortcuts();
            this.loadSettings();
        }

        /**
         * Bind event handlers
         */
        bindEvents() {
            const self = this;

            // Main toggle button
            $(document).on('click.mapAccessibility', '#map-main-toggle', function(e) {
                e.preventDefault();
                self.toggleWidget();
            });

            // Close panel button
            $(document).on('click.mapAccessibility', '#map-close-panel', function(e) {
                e.preventDefault();
                self.closeWidget();
            });

            // TTS toggle button - New functionality
            $(document).on('click.mapAccessibility', '#map-tts-toggle', function(e) {
                e.preventDefault();
                self.toggleTextSelection();
            });

            // Dyslexic Font toggle button
            $(document).on('click.mapAccessibility', '#map-dyslexic-font-toggle', function(e) {
                e.preventDefault();
                self.toggleDyslexicFont();
            });

            // Reading Guide toggle button
            $(document).on('click.mapAccessibility', '#map-reading-guide-toggle', function(e) {
                e.preventDefault();
                self.toggleReadingGuide();
            });

            // Font Size toggle button
            $(document).on('click.mapAccessibility', '#map-font-size-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleFontSizeControls();
            });

            // Line Spacing toggle button
            $(document).on('click.mapAccessibility', '#map-line-spacing-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleLineSpacingControls();
            });

            // Font Size control buttons
            $(document).on('click.mapAccessibility', '#map-font-size-increase', function(e) {
                e.preventDefault();
                self.increaseFontSize();
            });

            $(document).on('click.mapAccessibility', '#map-font-size-decrease', function(e) {
                e.preventDefault();
                self.decreaseFontSize();
            });

            $(document).on('click.mapAccessibility', '#map-font-size-reset', function(e) {
                e.preventDefault();
                self.resetFontSize();
            });

            // Category Reset button
            $(document).on('click.mapAccessibility', '#map-reset-category', function(e) {
                e.preventDefault();
                self.resetCurrentCategory();
            });

            // Contrast Themes toggle functionality
            $(document).on('click.mapAccessibility', '#map-contrast-themes-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleContrastThemesSection();
            });

            // Custom Theme Colors functionality - Use event delegation for modal content
            $(document).on('click.mapAccessibility', '#map-custom-theme-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleCustomThemeSection();
            });

            $(document).on('input.mapAccessibility change.mapAccessibility click.mapAccessibility', '.map-color-picker, .map-color-picker-compact', function() {
                self.applyIndividualColorChange($(this));
                self.updateColorPreview($(this));
                self.toggleResetButtonVisibility($(this));
            });

            // Individual color reset buttons
            $(document).on('click.mapAccessibility', '.map-color-reset, .map-color-reset-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const targetId = $(this).data('target');
                self.resetIndividualColor(targetId);
            });

            // Make entire color row clickable to open color picker
            $(document).on('click.mapAccessibility', '.map-color-row', function(e) {
                // Don't trigger if clicking on the color input itself or reset button
                if ($(e.target).is('.map-color-picker-compact, .map-color-reset-btn, .map-color-reset-btn svg, .map-color-reset-btn path')) {
                    return;
                }

                e.preventDefault();
                const $colorInput = $(this).find('.map-color-picker-compact');
                if ($colorInput.length) {
                    $colorInput[0].click(); // Trigger native color picker
                }
            });





            // Line Spacing controls
            $(document).on('input.mapAccessibility', '#map-line-spacing-slider', function(e) {
                const spacing = parseFloat($(this).val());
                self.setLineSpacing(spacing);
            });

            $(document).on('click.mapAccessibility', '#map-line-spacing-reset', function(e) {
                e.preventDefault();
                self.resetLineSpacing();
            });

            // New Theme Selector Navigation
            $(document).on('click.mapAccessibility', '#map-theme-prev', function() {
                self.navigateTheme('prev');
            });

            $(document).on('click.mapAccessibility', '#map-theme-next', function() {
                self.navigateTheme('next');
            });

            // Theme dots navigation
            $(document).on('click.mapAccessibility', '.map-theme-dot', function() {
                const theme = $(this).data('theme');
                if (theme) {
                    self.selectTheme(theme);
                }
            });



            // Keyboard navigation for theme selector
            $(document).on('keydown.mapAccessibility', '.map-theme-selector', function(e) {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    self.navigateTheme('prev');
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    self.navigateTheme('next');
                } else if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    // Theme is already applied by navigation, just add visual feedback
                    self.showThemeAppliedFeedback();
                }
            });

            // Stop button
            $(document).on('click.mapAccessibility', '#map-tts-stop', function(e) {
                e.preventDefault();
                self.stopSpeech();
            });

            // Modal system event handlers
            // Category buttons
            $(document).on('click.mapAccessibility', '.map-category-button:not(.map-category-disabled)', function(e) {
                e.preventDefault();
                const category = $(this).data('category');
                if (category) {
                    self.showSection(category);
                }
            });

            // Back buttons (including header back button)
            $(document).on('click.mapAccessibility', '.map-back-button, #map-header-back-button', function(e) {
                e.preventDefault();
                self.showMainMenu();
            });

            // Keyboard navigation for category buttons
            $(document).on('keydown.mapAccessibility', '.map-category-button', function(e) {
                self.handleCategoryKeyNavigation(e, $(this));
            });

            // Speed control
            $(document).on('input', '#map-speed-control', function() {
                const speed = parseFloat($(this).val());
                self.updateSpeechRate(speed);
                $('.map-speed-value').text(speed + 'x');
                // Save the new speed setting
                self.saveUserPreferences();
            });

            // Highlight text checkbox
            $(document).on('change', '#map-highlight-text', function() {
                self.highlightEnabled = $(this).is(':checked');
                self.saveUserPreferences();
            });

            // Auto-scroll checkbox
            $(document).on('change', '#map-auto-scroll', function() {
                self.autoScrollEnabled = $(this).is(':checked');
                self.saveUserPreferences();
            });

            // Highlight toggle
            $(document).on('change', '#map-highlight-text', function() {
                self.highlightEnabled = $(this).is(':checked');
            });

            // Auto scroll toggle
            $(document).on('change', '#map-auto-scroll', function() {
                self.autoScrollEnabled = $(this).is(':checked');
            });

            // Handle speech synthesis events
            if (this.speechSynthesis) {
                this.speechSynthesis.addEventListener('voiceschanged', () => {
                    this.loadVoices();
                });
            }
        }

        /**
         * Setup keyboard shortcuts
         */
        setupKeyboardShortcuts() {
            const self = this;

            $(document).on('keydown.mapAccessibility', function(e) {
                // Alt + A: Toggle widget
                if (e.altKey && e.key.toLowerCase() === 'a') {
                    e.preventDefault();
                    self.toggleWidget();
                }

                // Alt + S: Toggle speech
                if (e.altKey && e.key.toLowerCase() === 's') {
                    e.preventDefault();
                    if ($('#map-widget-panel').is(':visible')) {
                        self.toggleSpeech();
                    }
                }

                // Alt + X: Stop speech
                if (e.altKey && e.key.toLowerCase() === 'x') {
                    e.preventDefault();
                    self.stopSpeech();
                }

                // Escape: Navigate back or close widget
                if (e.key === 'Escape') {
                    if ($('#map-widget-panel').is(':visible')) {
                        e.preventDefault();
                        // If we're in a category view, go back to main menu
                        if (self.currentView !== 'main-menu') {
                            self.showMainMenu();
                        } else {
                            // If we're in main menu, close the widget
                            self.closeWidget();
                        }
                    }
                }
            });
        }

        /**
         * Load user settings and preferences
         */
        loadSettings() {
            // Load settings from localized data if available
            if (typeof mapAjax !== 'undefined' && mapAjax.settings) {
                const settings = mapAjax.settings;

                $('#map-speed-control').val(settings.speech_rate);
                $('.map-speed-value').text(settings.speech_rate + 'x');

                this.highlightEnabled = $('#map-highlight-text').is(':checked');
                this.autoScrollEnabled = $('#map-auto-scroll').is(':checked');
            } else {
                // Use default settings if mapAjax is not available
                this.highlightEnabled = false;
                this.autoScrollEnabled = false;
            }

            // Load user preferences from localStorage
            this.loadUserPreferences();
        }

        /**
         * Load user preferences from localStorage
         */
        loadUserPreferences() {
            try {
                // Load text-to-speech activation state
                const ttsActive = localStorage.getItem('map_tts_active');
                if (ttsActive === 'true') {
                    this.isTextToSpeechActive = true;
                    // Restore the active state
                    this.restoreTextToSpeechState();
                }

                // Load other preferences
                const speechRate = localStorage.getItem('map_speech_rate');
                if (speechRate) {
                    $('#map-speed-control').val(parseFloat(speechRate));
                    $('.map-speed-value').text(speechRate + 'x');
                }

                const highlightEnabled = localStorage.getItem('map_highlight_enabled');
                if (highlightEnabled !== null) {
                    this.highlightEnabled = highlightEnabled === 'true';
                    $('#map-highlight-text').prop('checked', this.highlightEnabled);
                }

                const autoScrollEnabled = localStorage.getItem('map_autoscroll_enabled');
                if (autoScrollEnabled !== null) {
                    this.autoScrollEnabled = autoScrollEnabled === 'true';
                    $('#map-auto-scroll').prop('checked', this.autoScrollEnabled);
                }

                // Load dyslexic font state
                const dyslexicFontActive = localStorage.getItem('map_dyslexic_font_active');
                if (dyslexicFontActive === 'true') {
                    this.isDyslexicFontActive = true;
                    this.restoreDyslexicFontState();
                }

                // Load reading guide state
                const readingGuideActive = localStorage.getItem('map_reading_guide_active');
                if (readingGuideActive === 'true') {
                    this.isReadingGuideActive = true;
                    this.restoreReadingGuideState();
                }

                // Load ADHD Focus Mode state
                const adhdFocusModeActive = localStorage.getItem('map_adhd_focus_mode');
                if (adhdFocusModeActive === 'true') {
                    this.adhdFocusMode = true;
                    this.restoreADHDFocusModeState();
                }

                // Load Big Cursor Mode state
                const bigCursorModeActive = localStorage.getItem('map_big_cursor_mode');
                if (bigCursorModeActive === 'true') {
                    this.bigCursorMode = true;
                    this.restoreBigCursorModeState();
                }

                // Load Text Magnification Mode state
                const textMagnificationModeActive = localStorage.getItem('map_text_magnification_mode');
                if (textMagnificationModeActive === 'true') {
                    this.textMagnificationMode = true;
                    this.restoreTextMagnificationModeState();
                }

                // Load font size state
                const savedFontSize = localStorage.getItem('map_font_size');
                if (savedFontSize !== null) {
                    this.currentFontSize = parseInt(savedFontSize, 10);
                    this.restoreFontSizeState();
                }

                // Load line spacing state
                const savedLineSpacing = localStorage.getItem('map_line_spacing');
                if (savedLineSpacing !== null) {
                    this.currentLineSpacing = parseFloat(savedLineSpacing);
                    this.restoreLineSpacingState();
                }

                // Load contrast theme state
                const savedContrastTheme = localStorage.getItem('map_contrast_theme');
                if (savedContrastTheme !== null) {
                    this.currentContrastTheme = savedContrastTheme;
                }

                // Load custom theme colors and state
                const savedCustomThemeColors = localStorage.getItem('map_custom_theme_colors');
                const savedCustomThemeActive = localStorage.getItem('map_custom_theme_active');
                if (savedCustomThemeColors !== null) {
                    try {
                        this.customThemeColors = JSON.parse(savedCustomThemeColors);
                    } catch (e) {
                        // Silent error handling
                    }
                }
                if (savedCustomThemeActive === 'true') {
                    this.isCustomThemeActive = true;
                    // Override contrast theme to 'custom' when custom theme is active
                    this.currentContrastTheme = 'custom';
                }

                // Load dark mode state
                const darkModeActive = localStorage.getItem('map_dark_mode_active');
                if (darkModeActive === 'true') {
                    this.isDarkModeActive = true;
                    this.restoreDarkModeState();
                }

                // Restore contrast theme state AFTER loading custom theme data
                this.restoreContrastThemeState();



            } catch (e) {
                // Silent error handling
            }
        }

        /**
         * Save user preferences to localStorage
         */
        saveUserPreferences() {
            try {
                // Save text-to-speech activation state
                localStorage.setItem('map_tts_active', this.isTextToSpeechActive.toString());

                // Save speech rate
                const speechRate = $('#map-speed-control').val();
                if (speechRate) {
                    localStorage.setItem('map_speech_rate', speechRate);
                }

                // Save other preferences
                localStorage.setItem('map_highlight_enabled', this.highlightEnabled.toString());
                localStorage.setItem('map_autoscroll_enabled', this.autoScrollEnabled.toString());
                localStorage.setItem('map_dyslexic_font_active', this.isDyslexicFontActive.toString());
                localStorage.setItem('map_reading_guide_active', this.isReadingGuideActive.toString());
                localStorage.setItem('map_adhd_focus_mode', this.adhdFocusMode.toString());
                localStorage.setItem('map_big_cursor_mode', this.bigCursorMode.toString());
                localStorage.setItem('map_text_magnification_mode', this.textMagnificationMode.toString());
                localStorage.setItem('map_font_size', this.currentFontSize.toString());
                localStorage.setItem('map_line_spacing', this.currentLineSpacing.toString());
                localStorage.setItem('map_contrast_theme', this.currentContrastTheme);

                // Save custom theme colors and state
                localStorage.setItem('map_custom_theme_colors', JSON.stringify(this.customThemeColors));
                localStorage.setItem('map_custom_theme_active', this.isCustomThemeActive.toString());

                // Save dark mode state
                localStorage.setItem('map_dark_mode_active', this.isDarkModeActive.toString());



                // Save current modal view (but don't restore it on page load - always start with main menu)
                localStorage.setItem('map_last_view', this.currentView);

            } catch (e) {
                // Silent error handling
            }
        }

        /**
         * Restore text-to-speech active state
         */
        restoreTextToSpeechState() {
            // Set the toggle button to active state
            const $toggle = $('#map-tts-toggle');
            if ($toggle.length) {
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');

                // Enable text selection
                this.enableTextSelection();

                // Do NOT play welcome message when restoring state from localStorage
                // Welcome message should only play when user manually activates TTS
            }
        }



        /**
         * Toggle widget visibility
         */
        toggleWidget() {
            const $toggle = $('#map-main-toggle');
            const $panel = $('#map-widget-panel');
            const isExpanded = $toggle.attr('aria-expanded') === 'true';

            if (isExpanded) {
                this.closeWidget();
            } else {
                this.openWidget();
            }
        }

        /**
         * Open widget panel with professional button fade
         */
        openWidget() {
            const $toggle = $('#map-main-toggle');
            const $panel = $('#map-widget-panel');
            const $widget = $('#map-accessibility-widget');

            // 1. Reset any ongoing transitions
            this.isTransitioning = false;

            // 2. Immediately show panel at full size and mark as active
            $toggle.attr('aria-expanded', 'true');
            $panel.show().attr('aria-hidden', 'false');
            $widget.addClass('map-panel-active');
            $toggle.addClass('map-panel-open');

            // 3. Force reset to main menu state immediately (no animation)
            $('.map-modal-view').removeClass('map-modal-view-active');
            $('#map-main-menu').addClass('map-modal-view-active');
            this.currentView = 'main-menu';
            this.currentCategory = null;

            // 4. Initialize header state for main menu
            $('#map-panel-title').show();
            $('#map-header-navigation').hide();

            // 5. Hide reset button when opening to main menu
            this.hideResetButton();

            // 6. Start button fade immediately (not after panel animation)
            setTimeout(() => {
                $toggle.removeClass('map-panel-open').addClass('map-fading-out');
            }, 100); // Very brief delay to show button over panel

            // 7. Ensure main menu is visible and properly initialized
            setTimeout(() => {
                this.ensureMainMenuVisible();
            }, 50);

            // 8. Focus first interactive element after button starts fading
            setTimeout(() => {
                $panel.find('button, input, select').first().focus();
            }, 200);
        }

        /**
         * Ensure main menu is visible and properly set up
         */
        ensureMainMenuVisible() {
            const $mainMenu = $('#map-main-menu');
            const $allViews = $('.map-modal-view');

            // Force hide all views first
            $allViews.removeClass('map-modal-view-active').hide();

            // Force show main menu
            $mainMenu.addClass('map-modal-view-active').show();

            // Ensure proper state
            this.currentView = 'main-menu';
            this.currentCategory = null;

            // Ensure header is correct
            $('#map-panel-title').show();
            $('#map-header-navigation').hide();
            this.hideResetButton();
        }

        /**
         * Close widget panel and restore button
         */
        closeWidget() {
            const $toggle = $('#map-main-toggle');
            const $panel = $('#map-widget-panel');
            const $widget = $('#map-accessibility-widget');

            // 1. Stop any ongoing transitions
            this.isTransitioning = false;

            // 2. Immediately restore button visibility
            $toggle.removeClass('map-fading-out map-panel-open');
            $widget.removeClass('map-panel-active');

            // 3. Hide panel and update ARIA
            $toggle.attr('aria-expanded', 'false');
            $panel.hide().attr('aria-hidden', 'true');

            // 4. Reset view state immediately (no animation needed when closed)
            $('.map-modal-view').removeClass('map-modal-view-active');
            $('#map-main-menu').addClass('map-modal-view-active');
            this.currentView = 'main-menu';
            this.currentCategory = null;

            // 5. Reset header state
            $('#map-panel-title').show();
            $('#map-header-navigation').hide();

            // 6. Ensure reset button is hidden when closing
            this.hideResetButton();

            // 7. Return focus to toggle button
            $toggle.focus();
        }

        /**
         * Show main menu (Level 1) with premium animation
         */
        showMainMenu() {
            if (this.isTransitioning) return;

            this.previousView = this.currentView;
            this.currentView = 'main-menu';
            this.currentCategory = null;

            // Hide reset button when back to main menu
            this.hideResetButton();

            // Update header: show title, hide navigation
            $('#map-panel-title').show();
            $('#map-header-navigation').hide();

            // Use premium transition animation
            this.switchView('map-main-menu');
        }

        /**
         * Show specific section (Level 2)
         * @param {string} section - The section to show (text, colors, navigation, preferences)
         */
        showSection(section) {
            if (this.isTransitioning) return;

            const validSections = ['text', 'colors', 'navigation', 'preferences'];
            if (!validSections.includes(section)) {
                return;
            }

            this.previousView = this.currentView;
            this.currentView = section;
            this.currentCategory = section;

            // Update header: hide title, show navigation with category title
            $('#map-panel-title').hide();
            $('#map-header-navigation').show();

            // Set the category title from the view's data-title attribute
            const $view = $(`#map-view-${section}`);
            const categoryTitle = $view.attr('data-title') || section.charAt(0).toUpperCase() + section.slice(1);
            $('#map-header-category-title').text(categoryTitle);

            // Show reset button when inside a category
            this.showResetButton();

            // Initialize section-specific features
            if (section === 'colors') {
                // Initialize contrast themes and custom theme colors when colors section is shown
                setTimeout(() => {
                    this.initializeContrastThemesSection();
                    this.initializeCustomThemeColorsSection();
                }, 100);

            } else if (section === 'navigation') {
                // Initialize navigation features (same as text for now)
                setTimeout(() => {
                    this.initializeNavigationSection();
                }, 100);
            } else if (section === 'preferences') {
                // Initialize preferences features
                setTimeout(() => {
                    this.initializePreferencesSection();
                }, 100);
            }

            this.switchView(`map-view-${section}`);
        }

        /**
         * Switch between modal views with premium smooth transitions
         * @param {string} targetViewId - The ID of the target view element
         */
        switchView(targetViewId) {
            if (this.isTransitioning) return;

            this.isTransitioning = true;
            const $currentView = $('.map-modal-view-active');
            const $targetView = $(`#${targetViewId}`);

            if ($targetView.length === 0) {
                this.isTransitioning = false;
                return;
            }

            // Determine animation direction based on navigation
            const isForward = this.isNavigatingForward(targetViewId);
            const animationDuration = 450; // Match CSS animation duration

            // Start premium transition animation
            this.performPremiumTransition($currentView, $targetView, isForward, animationDuration);
        }

        /**
         * Determine if navigation is forward (category → options) or backward (options → category)
         * @param {string} targetViewId - The target view ID
         * @returns {boolean} True if navigating forward
         */
        isNavigatingForward(targetViewId) {
            const currentViewId = $('.map-modal-view-active').attr('id');

            // Forward: main-menu → category view
            if (currentViewId === 'map-main-menu' && targetViewId.startsWith('map-view-')) {
                return true;
            }

            // Backward: category view → main-menu
            if (currentViewId && currentViewId.startsWith('map-view-') && targetViewId === 'map-main-menu') {
                return false;
            }

            // Default to forward for other cases
            return true;
        }

        /**
         * Perform premium transition animation between views
         * @param {jQuery} $currentView - Current active view
         * @param {jQuery} $targetView - Target view to show
         * @param {boolean} isForward - Direction of navigation
         * @param {number} duration - Animation duration in ms
         */
        performPremiumTransition($currentView, $targetView, isForward, duration) {
            const forwardClass = isForward ? 'forward' : 'backward';

            // Prepare target view for animation
            $targetView.removeClass('map-modal-view-active')
                      .addClass(`map-view-entering-${forwardClass}`)
                      .show();

            // Animate current view out
            if ($currentView.length > 0) {
                $currentView.addClass(`map-view-exiting-${forwardClass}`);
            }

            // Complete transition after animation
            setTimeout(() => {
                // Clean up current view
                if ($currentView.length > 0) {
                    $currentView.removeClass('map-modal-view-active')
                              .removeClass(`map-view-exiting-${forwardClass}`)
                              .hide();
                }

                // Activate target view
                $targetView.removeClass(`map-view-entering-${forwardClass}`)
                          .addClass('map-modal-view-active');

                // Reset transition state
                this.isTransitioning = false;

                // Focus management with slight delay for smooth UX
                setTimeout(() => {
                    this.manageFocusAfterTransition($targetView);
                }, 100);

                // Save current view state
                this.saveUserPreferences();
            }, duration);
        }

        /**
         * Manage focus after view transitions
         * @param {jQuery} $targetView - The target view element
         */
        manageFocusAfterTransition($targetView) {
            // Focus the first interactive element in the new view for keyboard accessibility
            const $firstInteractive = $targetView.find('button, input, select, [tabindex]:not([tabindex="-1"])').first();
            if ($firstInteractive.length > 0) {
                $firstInteractive.focus();
            }
        }

        /**
         * Handle keyboard navigation for category buttons
         * @param {Event} e - The keyboard event
         * @param {jQuery} $currentButton - The currently focused button
         */
        handleCategoryKeyNavigation(e, $currentButton) {
            const $categoryButtons = $('.map-category-button:not(.map-category-disabled)');
            const currentIndex = $categoryButtons.index($currentButton);
            let targetIndex = currentIndex;

            switch (e.key) {
                case 'ArrowDown':
                case 'ArrowRight':
                    e.preventDefault();
                    targetIndex = (currentIndex + 1) % $categoryButtons.length;
                    break;
                case 'ArrowUp':
                case 'ArrowLeft':
                    e.preventDefault();
                    targetIndex = (currentIndex - 1 + $categoryButtons.length) % $categoryButtons.length;
                    break;
                case 'Home':
                    e.preventDefault();
                    targetIndex = 0;
                    break;
                case 'End':
                    e.preventDefault();
                    targetIndex = $categoryButtons.length - 1;
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    $currentButton.click();
                    return;
                default:
                    return;
            }

            $categoryButtons.eq(targetIndex).focus();
        }

        /**
         * Toggle dyslexic font
         */
        toggleDyslexicFont() {
            const $toggle = $('#map-dyslexic-font-toggle');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Disable dyslexic font
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.disableDyslexicFont();
                this.isDyslexicFontActive = false;
            } else {
                // Enable dyslexic font
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.enableDyslexicFont();
                this.isDyslexicFontActive = true;
            }

            // Save the state to localStorage
            this.saveUserPreferences();
        }

        /**
         * Enable dyslexic font
         */
        enableDyslexicFont() {
            // Add the dyslexic font class to body
            $('body').addClass('dyslexic-font');

            // Add loading class temporarily for smooth transition
            $('body').addClass('dyslexic-font-loading');

            // Remove loading class after font loads
            setTimeout(() => {
                $('body').removeClass('dyslexic-font-loading');
            }, 500);
        }

        /**
         * Disable dyslexic font
         */
        disableDyslexicFont() {
            // Remove the dyslexic font class from body
            $('body').removeClass('dyslexic-font dyslexic-font-loading');
        }

        /**
         * Restore dyslexic font state from localStorage
         */
        restoreDyslexicFontState() {
            // Set the toggle button to active state
            const $toggle = $('#map-dyslexic-font-toggle');
            if ($toggle.length) {
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');

                // Enable dyslexic font
                this.enableDyslexicFont();
            }
        }

        /**
         * Toggle reading guide
         */
        toggleReadingGuide() {
            const $toggle = $('#map-reading-guide-toggle');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Disable reading guide
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.disableReadingGuide();
                this.isReadingGuideActive = false;
            } else {
                // Enable reading guide
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.enableReadingGuide();
                this.isReadingGuideActive = true;
            }

            // Save the state to localStorage
            this.saveUserPreferences();
        }

        /**
         * Enable reading guide
         */
        enableReadingGuide() {
            // Remove any existing guide line first to prevent duplicates
            $('#map-reading-guide-line').remove();

            // Remove any existing mouse move listeners to prevent conflicts
            $(document).off('mousemove.mapReadingGuide');

            // Create the reading guide element
            $('body').append('<div id="map-reading-guide-line" class="map-reading-guide hidden"></div>');

            // Add mouse move listener with a slight delay to ensure DOM is ready
            setTimeout(() => {
                $(document).on('mousemove.mapReadingGuide', (e) => {
                    this.updateReadingGuidePosition(e.clientY);
                });

                // Show the guide line
                const $guideLine = $('#map-reading-guide-line');
                if ($guideLine.length) {
                    $guideLine.removeClass('hidden');
                }
            }, 100);
        }

        /**
         * Disable reading guide
         */
        disableReadingGuide() {
            // Remove mouse move listener
            $(document).off('mousemove.mapReadingGuide');

            // Hide and remove the guide line immediately to prevent flickering
            const $guideLine = $('#map-reading-guide-line');
            if ($guideLine.length) {
                $guideLine.addClass('hidden');
                // Remove after a short transition
                setTimeout(() => {
                    $guideLine.remove();
                }, 100);
            }


        }

        /**
         * Update reading guide position
         */
        updateReadingGuidePosition(mouseY) {
            const $guideLine = $('#map-reading-guide-line');
            if ($guideLine.length && this.isReadingGuideActive) {
                // Position the guide line at the mouse Y position
                $guideLine.css('top', mouseY + 'px');

                // Add smooth class for better animation
                if (!$guideLine.hasClass('smooth')) {
                    $guideLine.addClass('smooth');
                }
            }
        }

        /**
         * Restore reading guide state from localStorage
         */
        restoreReadingGuideState() {
            // Use a longer delay to ensure all DOM elements are ready and other initializations are complete
            setTimeout(() => {
                // Enable the reading guide functionality
                this.enableReadingGuide();

                // Set the toggle button to active state
                const $toggle = $('#map-reading-guide-toggle');
                if ($toggle.length) {
                    $toggle.attr('data-active', 'true');
                    $toggle.addClass('active');

                    // Status message removed for cleaner UX


                } else {
                    // If toggle button still doesn't exist, try again with an even longer delay
                    setTimeout(() => {
                        const $toggleDelayed = $('#map-reading-guide-toggle');
                        if ($toggleDelayed.length) {
                            $toggleDelayed.attr('data-active', 'true');
                            $toggleDelayed.addClass('active');

                            const $statusDelayed = $('#map-reading-guide-status');
                            if ($statusDelayed.length) {
                                $statusDelayed.show();
                            }


                        }
                    }, 1000);
                }
            }, 200); // Initial delay to let other initializations complete
        }

        /**
         * Restore ADHD Focus Mode state from localStorage
         */
        restoreADHDFocusModeState() {
            setTimeout(() => {
                // Set the toggle button to active state
                const $toggle = $('#map-nav-adhd-focus-toggle');
                if ($toggle.length) {
                    $toggle.attr('data-active', 'true');
                    $toggle.addClass('active');

                    // Enable ADHD Focus Mode
                    $('body').addClass('map-adhd-focus-mode');
                    this.initializeADHDFocusMode();

                    // Show status message
                    const $status = $('#map-nav-adhd-focus-status');
                    if ($status.length) {
                        $status.show();
                    }
                }
            }, 200);
        }

        /**
         * Restore Big Cursor Mode state from localStorage
         */
        restoreBigCursorModeState() {
            setTimeout(() => {
                // Set the toggle button to active state
                const $toggle = $('#map-big-cursor-toggle');
                if ($toggle.length) {
                    $toggle.attr('data-active', 'true');
                    $toggle.addClass('active');

                    // Enable Big Cursor Mode
                    $('body').addClass('map-big-cursor-mode');

                    // Show status message
                    const $status = $('#map-big-cursor-status');
                    if ($status.length) {
                        $status.show();
                    }
                }
            }, 200);
        }

        /**
         * Restore Text Magnification Mode state from localStorage
         */
        restoreTextMagnificationModeState() {
            setTimeout(() => {
                // Set the toggle button to active state
                const $toggle = $('#map-text-magnification-toggle');
                if ($toggle.length) {
                    $toggle.attr('data-active', 'true');
                    $toggle.addClass('active');

                    // Enable Text Magnification Mode
                    this.enableTextMagnification();

                    // Show status message
                    const $status = $('#map-text-magnification-status');
                    if ($status.length) {
                        $status.show();
                    }
                }
            }, 200);
        }

        /**
         * Toggle font size controls visibility
         */
        toggleFontSizeControls() {
            const $toggle = $('#map-font-size-toggle');
            const $controls = $('#map-font-size-controls');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Hide controls
                $controls.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($toggle);
            } else {
                // Show controls
                $controls.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($toggle);
            }
        }

        /**
         * Toggle line spacing controls visibility
         */
        toggleLineSpacingControls() {
            const $toggle = $('#map-line-spacing-toggle');
            const $controls = $('#map-line-spacing-controls');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Hide controls
                $controls.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($toggle);
            } else {
                // Show controls
                $controls.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($toggle);
            }
        }

        /**
         * Increase font size
         */
        increaseFontSize() {
            // Increase by 5% increments, max 150%
            if (this.currentFontSize < 150) {
                this.currentFontSize += 5;
                this.applyFontSize();
                this.updateFontSizeDisplay();
                this.showFontSizeStatus();
                this.saveUserPreferences();

                // Show controls if they're hidden
                const $controls = $('#map-font-size-controls');
                if ($controls.length && !$controls.is(':visible')) {
                    $controls.slideDown(200);
                    const $toggle = $('#map-font-size-toggle');
                    if ($toggle.length) {
                        $toggle.attr('data-active', 'true');
                        $toggle.addClass('active');
                    }
                }
            }

            this.updateFontSizeButtons();
        }

        /**
         * Decrease font size
         */
        decreaseFontSize() {
            // Decrease by 5% increments, min 75%
            if (this.currentFontSize > 75) {
                this.currentFontSize -= 5;
                this.applyFontSize();
                this.updateFontSizeDisplay();
                this.showFontSizeStatus();
                this.saveUserPreferences();

                // Show controls if they're hidden
                const $controls = $('#map-font-size-controls');
                if ($controls.length && !$controls.is(':visible')) {
                    $controls.slideDown(200);
                    const $toggle = $('#map-font-size-toggle');
                    if ($toggle.length) {
                        $toggle.attr('data-active', 'true');
                        $toggle.addClass('active');
                    }
                }
            }

            this.updateFontSizeButtons();
        }

        /**
         * Reset font size to default (100%)
         */
        resetFontSize() {
            this.currentFontSize = 100;
            this.applyFontSize();
            this.updateFontSizeDisplay();
            this.showFontSizeStatus();
            this.saveUserPreferences();
            this.updateFontSizeButtons();
        }

        /**
         * Apply font size to the page
         */
        applyFontSize() {
            // Remove any existing font size style
            $('#map-font-size-style').remove();

            // Apply the current font size using CSS (except for 100% which is default)
            if (this.currentFontSize !== 100) {
                const fontSizeRatio = this.currentFontSize / 100;
                const cssRules = `
                    html.map-font-size-active body,
                    html.map-font-size-active p:not(.map-accessibility-widget *),
                    html.map-font-size-active span:not(.map-accessibility-widget *),
                    html.map-font-size-active div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    html.map-font-size-active li:not(.map-accessibility-widget *),
                    html.map-font-size-active a:not(.map-accessibility-widget *),
                    html.map-font-size-active td:not(.map-accessibility-widget *),
                    html.map-font-size-active th:not(.map-accessibility-widget *),
                    html.map-font-size-active label:not(.map-accessibility-widget *),
                    html.map-font-size-active input:not(.map-accessibility-widget *),
                    html.map-font-size-active textarea:not(.map-accessibility-widget *),
                    html.map-font-size-active select:not(.map-accessibility-widget *),
                    html.map-font-size-active button:not(.map-accessibility-widget *),
                    html.map-font-size-active h1:not(.map-accessibility-widget *),
                    html.map-font-size-active h2:not(.map-accessibility-widget *),
                    html.map-font-size-active h3:not(.map-accessibility-widget *),
                    html.map-font-size-active h4:not(.map-accessibility-widget *),
                    html.map-font-size-active h5:not(.map-accessibility-widget *),
                    html.map-font-size-active h6:not(.map-accessibility-widget *) {
                        font-size: ${fontSizeRatio}em !important;
                    }
                `;

                // Add the CSS to the page
                $('<style id="map-font-size-style">' + cssRules + '</style>').appendTo('head');
                $('html').addClass('map-font-size-active');
            } else {
                $('html').removeClass('map-font-size-active');
            }


        }

        /**
         * Update font size display
         */
        updateFontSizeDisplay() {
            let displayText;

            if (this.currentFontSize === 100) {
                displayText = 'Default';
            } else if (this.currentFontSize > 100) {
                const increase = this.currentFontSize - 100;
                displayText = `+${increase}%`;
            } else {
                const decrease = 100 - this.currentFontSize;
                displayText = `−${decrease}%`;
            }

            // Update display elements
            $('#map-font-size-value').text(displayText);
            $('#map-font-percentage').text(`${this.currentFontSize}%`);

            // Update size preview
            const $preview = $('.map-size-preview');
            if ($preview.length) {
                const scaleFactor = this.currentFontSize / 100;
                $preview.css('font-size', `${24 * scaleFactor}px`);
            }

            // Note: Toggle state is managed by user clicks on the toggle button only
            // Reset button should not affect the toggle's active state
        }

        /**
         * Update font size button states
         */
        updateFontSizeButtons() {
            const $increaseBtn = $('#map-font-size-increase');
            const $decreaseBtn = $('#map-font-size-decrease');

            // Disable increase button at maximum (150%)
            if (this.currentFontSize >= 150) {
                $increaseBtn.prop('disabled', true);
            } else {
                $increaseBtn.prop('disabled', false);
            }

            // Disable decrease button at minimum (75%)
            if (this.currentFontSize <= 75) {
                $decreaseBtn.prop('disabled', true);
            } else {
                $decreaseBtn.prop('disabled', false);
            }
        }

        /**
         * Show font size status message - Disabled for cleaner interface
         */
        showFontSizeStatus() {
            // Status messages disabled to keep interface clean
            return;
        }

        /**
         * Restore font size state from localStorage
         */
        restoreFontSizeState() {
            // Apply the saved font size
            this.applyFontSize();
            this.updateFontSizeDisplay();
            this.updateFontSizeButtons();

            // Show controls if font size is not default
            if (this.currentFontSize !== 100) {
                const $controls = $('#map-font-size-controls');
                if ($controls.length) {
                    $controls.show();
                }
            }


        }

        /**
         * Set line spacing
         * @param {number} spacing - Line spacing value (1.0 to 2.5)
         */
        setLineSpacing(spacing) {
            // Validate spacing value
            spacing = Math.max(1.0, Math.min(2.5, spacing));

            this.currentLineSpacing = spacing;
            this.applyLineSpacing();
            this.updateLineSpacingDisplay();
            this.showLineSpacingStatus();
            this.saveUserPreferences();

            // Show controls if they're hidden and spacing is not default
            if (spacing !== 1.5) {
                const $controls = $('#map-line-spacing-controls');
                if ($controls.length && !$controls.is(':visible')) {
                    $controls.slideDown(200);
                    const $toggle = $('#map-line-spacing-toggle');
                    if ($toggle.length) {
                        $toggle.attr('data-active', 'true');
                        $toggle.addClass('active');
                    }
                }
            }


        }

        /**
         * Reset line spacing to default (1.5)
         */
        resetLineSpacing() {
            this.currentLineSpacing = 1.5;
            this.applyLineSpacing();
            this.updateLineSpacingDisplay();
            this.updateLineSpacingSlider();
            this.showLineSpacingStatus();
            this.saveUserPreferences();


        }

        /**
         * Apply line spacing to the page
         */
        applyLineSpacing() {
            // Remove any existing line spacing style
            $('#map-line-spacing-style').remove();

            // Apply the current line spacing using CSS (except for 1.5 which is default)
            if (this.currentLineSpacing !== 1.5) {
                const cssRules = `
                    html.map-line-spacing-active body,
                    html.map-line-spacing-active p,
                    html.map-line-spacing-active span,
                    html.map-line-spacing-active div,
                    html.map-line-spacing-active li,
                    html.map-line-spacing-active a,
                    html.map-line-spacing-active td,
                    html.map-line-spacing-active th,
                    html.map-line-spacing-active label,
                    html.map-line-spacing-active h1,
                    html.map-line-spacing-active h2,
                    html.map-line-spacing-active h3,
                    html.map-line-spacing-active h4,
                    html.map-line-spacing-active h5,
                    html.map-line-spacing-active h6,
                    html.map-line-spacing-active blockquote,
                    html.map-line-spacing-active pre {
                        line-height: ${this.currentLineSpacing} !important;
                    }

                    /* Preserve accessibility widget line spacing */
                    html.map-line-spacing-active .map-accessibility-widget,
                    html.map-line-spacing-active .map-accessibility-widget * {
                        line-height: 1.4 !important;
                    }
                `;

                // Add the CSS to the page
                $('<style id="map-line-spacing-style">' + cssRules + '</style>').appendTo('head');
                $('html').addClass('map-line-spacing-active');
            } else {
                $('html').removeClass('map-line-spacing-active');
            }


        }

        /**
         * Update line spacing display
         */
        updateLineSpacingDisplay() {
            let displayText;

            if (this.currentLineSpacing === 1.5) {
                displayText = 'Default';
            } else if (this.currentLineSpacing < 1.5) {
                displayText = 'Tight';
            } else {
                displayText = 'Wide';
            }

            // Update display elements
            $('#map-line-spacing-value').text(displayText);
            $('#map-spacing-numeric').text(`${this.currentLineSpacing}x`);

            // Update preview lines
            const $previewLines = $('#map-spacing-preview');
            if ($previewLines.length) {
                $previewLines.css('line-height', this.currentLineSpacing);
            }

            // Update slider progress
            const progress = ((this.currentLineSpacing - 1.0) / (2.5 - 1.0)) * 100;
            $('#map-slider-progress').css('width', `${progress}%`);

            // Note: Toggle state is managed by user clicks on the toggle button only
            // Reset button should not affect the toggle's active state
        }

        /**
         * Update line spacing slider position
         */
        updateLineSpacingSlider() {
            $('#map-line-spacing-slider').val(this.currentLineSpacing);
        }

        /**
         * Show line spacing status message
         */
        showLineSpacingStatus() {
            const $status = $('#map-line-spacing-status');
            if ($status.length) {
                $status.show();

                // Hide after 3 seconds
                setTimeout(() => {
                    $status.fadeOut();
                }, 3000);
            }
        }

        /**
         * Restore line spacing state from localStorage
         */
        restoreLineSpacingState() {
            // Apply the saved line spacing
            this.applyLineSpacing();
            this.updateLineSpacingDisplay();
            this.updateLineSpacingSlider();

            // Show controls if line spacing is not default
            if (this.currentLineSpacing !== 1.5) {
                const $controls = $('#map-line-spacing-controls');
                if ($controls.length) {
                    $controls.show();
                }
            }


        }

        /**
         * Apply contrast theme
         * @param {string} theme - Theme name (normal, dark, high-contrast, sepia, colorblind)
         */
        applyContrastTheme(theme) {
            // Validate theme
            const validThemes = ['normal', 'monochrome', 'low-saturation', 'high-saturation', 'dark', 'high-contrast', 'sepia', 'colorblind', 'custom'];
            if (!validThemes.includes(theme)) {

                return;
            }

            // Remove all existing theme classes and custom styles
            $('body').removeClass('map-theme-normal map-theme-monochrome map-theme-low-saturation map-theme-high-saturation map-theme-dark map-theme-high-contrast map-theme-sepia map-theme-colorblind map-theme-custom');
            $('#map-custom-theme-styles').remove();

            // Apply new theme class
            if (theme !== 'normal') {
                if (theme === 'custom') {
                    // Apply custom theme
                    this.applyCustomTheme();
                    return; // Custom theme handles its own application
                } else {
                    $('body').addClass(`map-theme-${theme}`);
                }
            }

            // Update current theme
            this.currentContrastTheme = theme;

            // Update UI
            this.updateContrastThemeUI(theme);
            this.showContrastThemeStatus(theme);

            // Save preference
            this.saveUserPreferences();


        }

        /**
         * Update contrast theme UI
         * @param {string} theme - Current theme name
         */
        updateContrastThemeUI(theme) {
            // Update active button
            $('.map-theme-button').removeClass('map-theme-active');
            $(`#map-theme-${theme}`).addClass('map-theme-active');




        }

        /**
         * Show contrast theme status message
         * @param {string} theme - Applied theme name
         */
        showContrastThemeStatus(theme) {
            const $status = $('#map-contrast-theme-status');
            if ($status.length) {
                $status.show();

                // Hide after 3 seconds
                setTimeout(() => {
                    $status.fadeOut();
                }, 3000);
            }
        }

        /**
         * Restore contrast theme state from localStorage
         */
        restoreContrastThemeState() {
            // Apply the saved theme
            this.applyContrastTheme(this.currentContrastTheme);

            // If custom theme is active, ensure UI elements are properly initialized
            if (this.currentContrastTheme === 'custom' && this.isCustomThemeActive) {
                // Initialize custom theme colors in the UI (for when user opens the section later)
                setTimeout(() => {
                    this.initializeCustomThemeColors();
                }, 100);
            }

            // Update theme selector if it exists
            if ($('#map-theme-preview').length) {
                this.initializeThemeSelector();
            }


        }

        /**
         * Toggle contrast themes section visibility - Updated for feature-toggle structure
         */
        toggleContrastThemesSection() {
            const $toggle = $('#map-contrast-themes-toggle');
            const $content = $('#map-contrast-themes-content');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $content.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($toggle);
            } else {
                $content.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($toggle);
            }
        }

        /**
         * Toggle custom theme section visibility - Updated for feature-toggle structure
         */
        toggleCustomThemeSection() {
            const $toggle = $('#map-custom-theme-toggle');
            const $content = $('#map-custom-theme-content');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                $content.slideUp(200);
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($toggle);
            } else {
                $content.slideDown(200);
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($toggle);
                // Initialize color values when first opened
                this.initializeCustomThemeColors();

                // Ensure elements are clickable after opening
                setTimeout(() => {
                    this.ensureCustomThemeElementsClickable();
                }, 350); // After slide animation completes
            }
        }

        /**
         * Ensure custom theme elements are clickable (fix for mouse click issues)
         */
        ensureCustomThemeElementsClickable() {
            const $content = $('#map-custom-theme-content');
            if ($content.is(':visible')) {
                // Force clickable state on all interactive elements
                $content.find('.map-color-picker, .map-color-picker-compact, .map-custom-theme-btn').each(function() {
                    $(this).css({
                        'pointer-events': 'auto',
                        'position': 'relative',
                        'z-index': '10'
                    });
                });
            }
        }

        /**
         * Initialize custom theme color pickers with current values
         */
        initializeCustomThemeColors() {
            $('#map-custom-text-color').val(this.customThemeColors.text);
            $('#map-custom-bg-color').val(this.customThemeColors.background);
            $('#map-custom-link-color').val(this.customThemeColors.link);
            $('#map-custom-heading-color').val(this.customThemeColors.heading);
        }



        /**
         * Apply individual color change without affecting other colors
         */
        applyIndividualColorChange($colorInput) {
            const inputId = $colorInput.attr('id');
            const colorValue = $colorInput.val();

            // Only proceed if there's a color value
            if (!colorValue) {
                return;
            }

            // Update the specific color in our internal state
            switch (inputId) {
                case 'map-custom-text-color':
                    this.customThemeColors.text = colorValue;
                    break;
                case 'map-custom-bg-color':
                    this.customThemeColors.background = colorValue;
                    break;
                case 'map-custom-link-color':
                    this.customThemeColors.link = colorValue;
                    break;
                case 'map-custom-heading-color':
                    this.customThemeColors.heading = colorValue;
                    break;
                default:
                    return;
            }

            // Apply only the specific color change
            this.applySpecificColorToCSS(inputId, colorValue);

            // Update state
            this.isCustomThemeActive = true;
            this.currentContrastTheme = 'custom';

            // Save preferences
            this.saveUserPreferences();
        }

        /**
         * Apply specific color to CSS without affecting other colors
         */
        applySpecificColorToCSS(inputId, colorValue) {
            // Ensure body has custom theme class
            $('body').addClass('map-theme-custom');

            // Get or create the custom theme style element
            let $styleElement = $('#map-custom-theme-styles');
            if ($styleElement.length === 0) {
                $styleElement = $('<style id="map-custom-theme-styles"></style>');
                $('head').append($styleElement);
            }

            // Get existing CSS content
            let existingCSS = $styleElement.html();

            // Apply the specific color change
            switch (inputId) {
                case 'map-custom-text-color':
                    existingCSS = this.updateCSSColorProperty(existingCSS, 'text', colorValue);
                    break;
                case 'map-custom-bg-color':
                    existingCSS = this.updateCSSColorProperty(existingCSS, 'background', colorValue);
                    break;
                case 'map-custom-link-color':
                    existingCSS = this.updateCSSColorProperty(existingCSS, 'link', colorValue);
                    break;
                case 'map-custom-heading-color':
                    existingCSS = this.updateCSSColorProperty(existingCSS, 'heading', colorValue);
                    break;
            }

            // Update the style element
            $styleElement.html(existingCSS);
        }

        /**
         * Update specific color property in CSS string
         */
        updateCSSColorProperty(cssString, colorType, colorValue) {
            // If no existing CSS, create base structure
            if (!cssString) {
                cssString = `
                    body.map-theme-custom {
                        /* Base styles will be added as needed */
                    }
                    body.map-theme-custom p,
                    body.map-theme-custom div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    body.map-theme-custom span:not(.map-accessibility-widget *):not(.map-color-title):not(.map-color-desc),
                    body.map-theme-custom li,
                    body.map-theme-custom td,
                    body.map-theme-custom th {
                        /* Text color will be added as needed */
                    }
                    body.map-theme-custom h1,
                    body.map-theme-custom h2,
                    body.map-theme-custom h3,
                    body.map-theme-custom h4,
                    body.map-theme-custom h5,
                    body.map-theme-custom h6 {
                        /* Heading color will be added as needed */
                    }
                    body.map-theme-custom a {
                        /* Link color will be added as needed */
                    }
                    body.map-theme-custom main,
                    body.map-theme-custom article,
                    body.map-theme-custom section,
                    body.map-theme-custom .content,
                    body.map-theme-custom .post,
                    body.map-theme-custom .page {
                        /* Background color will be added as needed */
                    }
                `;
            }

            // Update specific color properties
            switch (colorType) {
                case 'text':
                    // Update text color in body and text elements
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s*{[^}]*?)(\s*color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 color: ${colorValue} !important; $3`
                    );
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s+p,[\s\S]*?th\s*{[^}]*?)(\s*color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 color: ${colorValue} !important; $3`
                    );
                    break;
                case 'background':
                    // Update background color in body and content elements
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s*{[^}]*?)(\s*background-color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 background-color: ${colorValue} !important; $3`
                    );
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s+main,[\s\S]*?\.page\s*{[^}]*?)(\s*background-color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 background-color: ${colorValue} !important; $3`
                    );
                    break;
                case 'heading':
                    // Update heading color
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s+h[1-6],[\s\S]*?h6\s*{[^}]*?)(\s*color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 color: ${colorValue} !important; $3`
                    );
                    break;
                case 'link':
                    // Update link color
                    cssString = cssString.replace(
                        /(body\.map-theme-custom\s+a\s*{[^}]*?)(\s*color:\s*[^;]+;\s*)?([^}]*})/,
                        `$1 color: ${colorValue} !important; $3`
                    );
                    break;
            }

            return cssString;
        }

        /**
         * Reset individual color input
         */
        resetIndividualColor(inputId) {
            const $input = $(`#${inputId}`);
            if ($input.length) {
                // Clear the input value
                $input.val('');

                // Update internal state
                switch (inputId) {
                    case 'map-custom-text-color':
                        this.customThemeColors.text = '';
                        break;
                    case 'map-custom-bg-color':
                        this.customThemeColors.background = '';
                        break;
                    case 'map-custom-link-color':
                        this.customThemeColors.link = '';
                        break;
                    case 'map-custom-heading-color':
                        this.customThemeColors.heading = '';
                        break;
                }

                // Remove the specific color from CSS
                this.removeColorFromCSS(inputId);

                // Update color preview
                this.updateColorPreview($input);

                // Hide reset button after reset
                this.toggleResetButtonVisibility($input);

                // Save preferences
                this.saveUserPreferences();

                // Show reset feedback
                this.showColorResetFeedback($input);
            }
        }

        /**
         * Remove specific color from CSS
         */
        removeColorFromCSS(inputId) {
            const $styleElement = $('#map-custom-theme-styles');
            if ($styleElement.length) {
                let cssString = $styleElement.html();

                switch (inputId) {
                    case 'map-custom-text-color':
                        // Remove text color properties
                        cssString = cssString.replace(/color:\s*[^;]+;\s*/g, '');
                        break;
                    case 'map-custom-bg-color':
                        // Remove background color properties
                        cssString = cssString.replace(/background-color:\s*[^;]+;\s*/g, '');
                        break;
                    case 'map-custom-link-color':
                        // Remove link color from a tags
                        cssString = cssString.replace(/(body\.map-theme-custom\s+a\s*{[^}]*?)color:\s*[^;]+;\s*([^}]*})/g, '$1$2');
                        break;
                    case 'map-custom-heading-color':
                        // Remove heading color from h tags
                        cssString = cssString.replace(/(body\.map-theme-custom\s+h[1-6],[\s\S]*?h6\s*{[^}]*?)color:\s*[^;]+;\s*([^}]*})/g, '$1$2');
                        break;
                }

                $styleElement.html(cssString);
            }
        }

        /**
         * Update color preview display
         */
        updateColorPreview($colorInput) {
            const inputId = $colorInput.attr('id');
            const colorValue = $colorInput.val();
            const $preview = $colorInput.siblings('.map-color-preview');
            const $previewText = $preview.find('.map-color-preview-text');

            if (colorValue) {
                // Update preview with selected color
                switch (inputId) {
                    case 'map-custom-text-color':
                        $previewText.css('color', colorValue);
                        $preview.css('background', 'rgba(255, 255, 255, 0.9)');
                        break;
                    case 'map-custom-bg-color':
                        $preview.css('background', colorValue);
                        // Adjust text color for contrast
                        const brightness = this.getColorBrightness(colorValue);
                        $previewText.css('color', brightness > 128 ? '#374151' : '#ffffff');
                        break;
                    case 'map-custom-link-color':
                        $previewText.css('color', colorValue);
                        $preview.css('background', 'rgba(255, 255, 255, 0.9)');
                        break;
                    case 'map-custom-heading-color':
                        $previewText.css('color', colorValue);
                        $preview.css('background', 'rgba(255, 255, 255, 0.9)');
                        break;
                }
            } else {
                // Reset preview to default
                $preview.css('background', 'rgba(255, 255, 255, 0.9)');
                $previewText.css('color', '#374151');
            }
        }

        /**
         * Get color brightness for contrast calculation
         */
        getColorBrightness(hexColor) {
            // Remove # if present
            hexColor = hexColor.replace('#', '');

            // Convert to RGB
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Calculate brightness using standard formula
            return (r * 299 + g * 587 + b * 114) / 1000;
        }



        /**
         * Show color reset feedback animation
         */
        showColorResetFeedback($input) {
            const $card = $input.closest('.map-color-card');
            $card.addClass('map-reset-feedback');

            setTimeout(() => {
                $card.removeClass('map-reset-feedback');
            }, 600);
        }

        /**
         * Apply custom theme automatically when colors change (with debouncing)
         */
        applyCustomThemeAutomatically() {
            try {
                // Clear existing timer
                if (this.customThemeDebounceTimer) {
                    clearTimeout(this.customThemeDebounceTimer);
                }

                // Update internal color values immediately for responsiveness
                const textColor = $('#map-custom-text-color').val() || '';
                const bgColor = $('#map-custom-bg-color').val() || '';
                const linkColor = $('#map-custom-link-color').val() || '';
                const headingColor = $('#map-custom-heading-color').val() || '';

                this.customThemeColors = {
                    text: textColor,
                    background: bgColor,
                    link: linkColor,
                    heading: headingColor
                };

                // Debounce the actual theme application to prevent performance issues
                this.customThemeDebounceTimer = setTimeout(() => {
                    try {
                        this.applyCustomTheme();
                    } catch (error) {

                    }
                }, 300); // 300ms delay
            } catch (error) {

            }
        }

        /**
         * Apply custom theme to the website
         */
        applyCustomTheme() {
            // Remove any existing contrast themes
            $('body').removeClass('map-theme-normal map-theme-monochrome map-theme-low-saturation map-theme-high-saturation map-theme-dark map-theme-high-contrast map-theme-sepia map-theme-colorblind');

            // Add custom theme class
            $('body').addClass('map-theme-custom');

            // Apply custom CSS variables
            const customCSS = `
                <style id="map-custom-theme-styles">
                    body.map-theme-custom {
                        background-color: ${this.customThemeColors.background} !important;
                        color: ${this.customThemeColors.text} !important;
                    }
                    body.map-theme-custom p,
                    body.map-theme-custom div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    body.map-theme-custom span:not(.map-accessibility-widget *):not(.map-color-title):not(.map-color-desc),
                    body.map-theme-custom li,
                    body.map-theme-custom td,
                    body.map-theme-custom th {
                        color: ${this.customThemeColors.text} !important;
                    }
                    body.map-theme-custom h1,
                    body.map-theme-custom h2,
                    body.map-theme-custom h3,
                    body.map-theme-custom h4,
                    body.map-theme-custom h5,
                    body.map-theme-custom h6 {
                        color: ${this.customThemeColors.heading} !important;
                    }
                    body.map-theme-custom a {
                        color: ${this.customThemeColors.link} !important;
                    }
                    body.map-theme-custom main,
                    body.map-theme-custom article,
                    body.map-theme-custom section,
                    body.map-theme-custom .content,
                    body.map-theme-custom .post,
                    body.map-theme-custom .page {
                        background-color: ${this.customThemeColors.background} !important;
                    }
                </style>
            `;

            // Remove existing custom theme styles
            $('#map-custom-theme-styles').remove();

            // Add new custom theme styles
            $('head').append(customCSS);

            // Update state
            this.isCustomThemeActive = true;
            this.currentContrastTheme = 'custom';

            // Show status message
            this.showCustomThemeStatus();

            // Save preferences
            this.saveUserPreferences();


        }



        /**
         * Show custom theme status message
         */
        showCustomThemeStatus() {
            const $status = $('#map-custom-theme-status');
            if ($status.length) {
                $status.show();

                // Hide after 3 seconds
                setTimeout(() => {
                    $status.fadeOut();
                }, 3000);
            }
        }

        /**
         * Initialize contrast themes section when colors view becomes active
         */
        initializeContrastThemesSection() {
            // Ensure the toggle button is properly set up
            const $toggle = $('#map-contrast-themes-toggle');
            const $content = $('#map-contrast-themes-content');

            if ($toggle.length && $content.length) {
                // Initialize as standard feature toggle
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');

                // Initialize theme selector
                this.initializeThemeSelector();
            }
        }

        /**
         * Initialize the modern theme selector
         */
        initializeThemeSelector() {
            // Set current theme index based on current theme
            this.currentThemeIndex = this.themes.findIndex(theme => theme.id === this.currentContrastTheme);
            if (this.currentThemeIndex === -1) this.currentThemeIndex = 0;

            this.selectedTheme = this.currentContrastTheme;
            this.updateThemeSelector();
        }

        /**
         * Navigate to previous/next theme
         * @param {string} direction - 'prev' or 'next'
         */
        navigateTheme(direction) {
            if (direction === 'prev') {
                this.currentThemeIndex = (this.currentThemeIndex - 1 + this.themes.length) % this.themes.length;
            } else {
                this.currentThemeIndex = (this.currentThemeIndex + 1) % this.themes.length;
            }

            this.selectedTheme = this.themes[this.currentThemeIndex].id;
            this.updateThemeSelector();

            // Apply theme immediately with visual feedback
            this.applyContrastTheme(this.selectedTheme);
            this.showThemeAppliedFeedback();
        }

        /**
         * Select a specific theme
         * @param {string} themeId - Theme ID to select
         */
        selectTheme(themeId) {
            const themeIndex = this.themes.findIndex(theme => theme.id === themeId);
            if (themeIndex !== -1) {
                this.currentThemeIndex = themeIndex;
                this.selectedTheme = themeId;
                this.updateThemeSelector();

                // Apply theme immediately with visual feedback
                this.applyContrastTheme(this.selectedTheme);
                this.showThemeAppliedFeedback();
            }
        }

        /**
         * Navigate theme selector to Default theme (for reset UX)
         */
        navigateToDefaultTheme() {
            // Find the "normal" (Default) theme index
            const defaultThemeIndex = this.themes.findIndex(theme => theme.id === 'normal');

            if (defaultThemeIndex !== -1) {
                // Update the theme selector to show Default theme
                this.currentThemeIndex = defaultThemeIndex;
                this.selectedTheme = 'normal';
                this.updateThemeSelector();

                // Add visual feedback that we've navigated to Default
                this.showThemeNavigationFeedback();
            }
        }

        /**
         * Show visual feedback when navigating to Default theme
         */
        showThemeNavigationFeedback() {
            const $iconPreview = $('#map-theme-icon-preview');
            if ($iconPreview.length) {
                // Add a subtle pulse animation to indicate navigation
                $iconPreview.addClass('applying');

                // Remove the animation class after it completes
                setTimeout(() => {
                    $iconPreview.removeClass('applying');
                }, 1200);
            }
        }

        /**
         * Update the theme selector UI
         */
        updateThemeSelector() {
            const currentTheme = this.themes[this.currentThemeIndex];

            // Update icon preview
            const $iconPreview = $('#map-theme-icon-preview');
            $iconPreview.html(currentTheme.icon);
            $iconPreview.attr('data-theme', currentTheme.id);

            // Update theme info - only name, no description
            $('#map-theme-name').text(currentTheme.name);

            // Update dots
            $('.map-theme-dot').removeClass('active');
            $(`.map-theme-dot[data-theme="${currentTheme.id}"]`).addClass('active');
        }

        /**
         * Show visual feedback when theme is applied
         */
        showThemeAppliedFeedback() {
            const $iconPreview = $('#map-theme-icon-preview');
            $iconPreview.addClass('applying');

            // Remove glow effect after animation
            setTimeout(() => {
                $iconPreview.removeClass('applying');
            }, 1000);
        }





        /**
         * Initialize custom theme colors section when colors view becomes active
         */
        initializeCustomThemeColorsSection() {
            // Ensure the toggle button is properly set up
            const $toggle = $('#map-custom-theme-toggle');
            const $content = $('#map-custom-theme-content');

            if ($toggle.length && $content.length) {
                // Initialize color pickers with current values
                this.initializeCustomThemeColors();

                // Initialize reset button visibility for all color pickers
                $content.find('.map-color-picker-compact').each((index, element) => {
                    this.toggleResetButtonVisibility($(element));
                });

                // Make sure the button is clickable
                $toggle.css({
                    'pointer-events': 'auto',
                    'cursor': 'pointer',
                    'z-index': '10'
                });

                // Add direct click handler as backup for modal content
                $toggle.off('click.customTheme').on('click.customTheme', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleCustomThemeSection();
                });

                // Ensure all interactive elements in the content are clickable
                $content.find('.map-color-picker, .map-color-picker-compact').css({
                    'pointer-events': 'auto',
                    'position': 'relative',
                    'z-index': '2'
                });

                // Add direct event handlers for color pickers as backup
                $content.find('.map-color-picker, .map-color-picker-compact').off('input.direct change.direct click.direct').on('input.direct change.direct click.direct', (e) => {
                    this.applyIndividualColorChange($(e.target));
                    this.toggleResetButtonVisibility($(e.target));
                });
            }
        }

        /**
         * Show reset button
         */
        showResetButton() {
            $('#map-reset-category').show();
        }

        /**
         * Hide reset button
         */
        hideResetButton() {
            $('#map-reset-category').hide();
        }

        /**
         * Reset current category options to default
         */
        resetCurrentCategory() {
            if (!this.currentCategory) {

                return;
            }

            switch (this.currentCategory) {
                case 'text':
                    this.resetTextCategory();
                    break;
                case 'colors':
                    this.resetColorsCategory();
                    break;
                case 'navigation':
                    this.resetNavigationCategory();
                    break;

                default:

            }


        }

        /**
         * Reset Text category options
         */
        resetTextCategory() {
            // Reset Text-to-Speech
            if (this.isTextToSpeechActive) {
                this.disableTextSelection();
                this.hideFloatingButton();
                this.stopSpeech();
                this.isTextToSpeechActive = false;

                const $ttsToggle = $('#map-tts-toggle');
                if ($ttsToggle.length) {
                    $ttsToggle.attr('data-active', 'false');
                    $ttsToggle.removeClass('active');
                    // Status message removed
                }
            }

            // Reset Dyslexic Font
            if (this.isDyslexicFontActive) {
                this.disableDyslexicFont();
                this.isDyslexicFontActive = false;

                const $dyslexicToggle = $('#map-dyslexic-font-toggle');
                if ($dyslexicToggle.length) {
                    $dyslexicToggle.attr('data-active', 'false');
                    $dyslexicToggle.removeClass('active');
                    // Status message removed
                }
            }

            // Reset Reading Guide
            if (this.isReadingGuideActive) {
                this.disableReadingGuide();
                this.isReadingGuideActive = false;

                const $readingGuideToggle = $('#map-reading-guide-toggle');
                if ($readingGuideToggle.length) {
                    $readingGuideToggle.attr('data-active', 'false');
                    $readingGuideToggle.removeClass('active');
                    // Status message removed
                }
            }

            // Reset Font Size
            this.currentFontSize = 100;
            this.applyFontSize();
            this.updateFontSizeDisplay();
            this.updateFontSizeButtons();

            // Hide font size controls and reset toggle state
            const $fontSizeControls = $('#map-font-size-controls');
            if ($fontSizeControls.length) {
                $fontSizeControls.hide();
            }

            const $fontSizeToggle = $('#map-font-size-toggle');
            if ($fontSizeToggle.length) {
                $fontSizeToggle.attr('data-active', 'false');
                $fontSizeToggle.removeClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($fontSizeToggle);
            }

            // Reset Line Spacing
            this.currentLineSpacing = 1.5;
            this.applyLineSpacing();
            this.updateLineSpacingDisplay();
            this.updateLineSpacingSlider();

            // Hide line spacing controls and reset toggle state
            const $lineSpacingControls = $('#map-line-spacing-controls');
            if ($lineSpacingControls.length) {
                $lineSpacingControls.hide();
            }

            const $lineSpacingToggle = $('#map-line-spacing-toggle');
            if ($lineSpacingToggle.length) {
                $lineSpacingToggle.attr('data-active', 'false');
                $lineSpacingToggle.removeClass('active');
                // Update arrow after state change
                this.toggleNavigationArrow($lineSpacingToggle);
            }

            // Save the reset state
            this.saveUserPreferences();

            // Success feedback removed for cleaner UX


        }

        /**
         * Reset Navigation category options (same as Text category)
         */
        resetNavigationCategory() {
            // Reset ADHD Focus Mode
            if (this.adhdFocusMode) {
                $('body').removeClass('map-adhd-focus-mode');
                $('#map-adhd-focus-overlay, #map-adhd-focus-border').hide();
                $(document).off('mousemove.adhd');
                this.adhdFocusMode = false;

                const $adhdToggle = $('#map-nav-adhd-focus-toggle');
                if ($adhdToggle.length) {
                    $adhdToggle.attr('data-active', 'false');
                    $adhdToggle.removeClass('active');
                }

                const $adhdStatus = $('#map-nav-adhd-focus-status');
                if ($adhdStatus.length) {
                    $adhdStatus.hide();
                }
            }

            // Reset Big Cursor Mode
            if (this.bigCursorMode) {
                $('body').removeClass('map-big-cursor-mode');
                this.bigCursorMode = false;

                const $bigCursorToggle = $('#map-big-cursor-toggle');
                if ($bigCursorToggle.length) {
                    $bigCursorToggle.attr('data-active', 'false');
                    $bigCursorToggle.removeClass('active');
                }

                const $bigCursorStatus = $('#map-big-cursor-status');
                if ($bigCursorStatus.length) {
                    $bigCursorStatus.hide();
                }
            }

            // Reset Text Magnification Mode
            if (this.textMagnificationMode) {
                this.disableTextMagnification();
                this.textMagnificationMode = false;

                const $textMagnificationToggle = $('#map-text-magnification-toggle');
                if ($textMagnificationToggle.length) {
                    $textMagnificationToggle.attr('data-active', 'false');
                    $textMagnificationToggle.removeClass('active');
                }

                const $textMagnificationStatus = $('#map-text-magnification-status');
                if ($textMagnificationStatus.length) {
                    $textMagnificationStatus.hide();
                }
            }

            // Save the reset state
            this.saveUserPreferences();
        }

        /**
         * Reset Colors category options
         */
        resetColorsCategory() {
            // Reset Contrast Theme to normal
            this.currentContrastTheme = 'normal';
            this.applyContrastTheme('normal');

            // Navigate theme selector to "Default" for better UX
            this.navigateToDefaultTheme();

            // Clear custom theme colors (no default colors)
            this.customThemeColors = {
                text: '',
                background: '',
                link: '',
                heading: ''
            };

            // Clear color pickers
            $('#map-custom-text-color').val('');
            $('#map-custom-bg-color').val('');
            $('#map-custom-link-color').val('');
            $('#map-custom-heading-color').val('');

            // Hide all color reset X buttons
            $('.map-color-reset-btn').removeClass('visible').hide();

            // Remove custom theme from website
            $('body').removeClass('map-theme-custom');
            $('#map-custom-theme-styles').remove();
            this.isCustomThemeActive = false;

            // Clear custom theme debounce timer
            if (this.customThemeDebounceTimer) {
                clearTimeout(this.customThemeDebounceTimer);
                this.customThemeDebounceTimer = null;
            }

            // Remove custom theme from localStorage
            try {
                localStorage.removeItem('map_custom_theme_colors');
                localStorage.removeItem('map_custom_theme_active');

            } catch (e) {

            }

            // Hide status messages
            $('#map-contrast-theme-status').hide();
            $('#map-custom-theme-status').hide();

            // Update theme selector UI if it exists
            if ($('#map-theme-preview').length) {
                this.initializeThemeSelector();
            }

            // Save the reset state (this will save the normal theme and reset custom theme flags)
            this.saveUserPreferences();

            // Show brief success message
            this.showResetSuccessMessage();


        }





        // Success message function removed for cleaner UX





        /**
         * Initialize navigation section features (duplicate of text features with nav- prefixes)
         */
        initializeNavigationSection() {
            // ADHD Focus Mode toggle
            $('#map-nav-adhd-focus-toggle').off('click.navigation').on('click.navigation', (e) => {
                e.preventDefault();
                this.toggleADHDFocusMode();
            });

            // Big Cursor toggle
            $('#map-big-cursor-toggle').off('click.navigation').on('click.navigation', (e) => {
                e.preventDefault();
                this.toggleBigCursor();
            });

            // Text Magnification toggle
            $('#map-text-magnification-toggle').off('click.navigation').on('click.navigation', (e) => {
                e.preventDefault();
                this.toggleTextMagnification();
            });
        }

        /**
         * Toggle ADHD Focus Mode
         */
        toggleADHDFocusMode() {
            const $toggle = $('#map-nav-adhd-focus-toggle');
            const $status = $('#map-nav-adhd-focus-status');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Disable ADHD Focus Mode
                $toggle.attr('data-active', 'false').removeClass('active');
                $('body').removeClass('map-adhd-focus-mode');
                $('#map-adhd-focus-overlay, #map-adhd-focus-border').hide();
                $(document).off('mousemove.adhd');
                $status.hide();
                this.adhdFocusMode = false;
            } else {
                // Enable ADHD Focus Mode
                $toggle.attr('data-active', 'true').addClass('active');
                $('body').addClass('map-adhd-focus-mode');
                $status.show();
                this.adhdFocusMode = true;
                this.initializeADHDFocusMode();
            }

            // Save preference
            this.saveUserPreferences();
        }

        /**
         * Initialize ADHD Focus Mode functionality
         */
        initializeADHDFocusMode() {
            // Remove existing overlay and border elements to ensure fresh creation
            $('#map-adhd-focus-overlay, #map-adhd-focus-border').remove();

            // Create focus overlay with updated opacity
            $('body').append(`
                <div id="map-adhd-focus-overlay" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.69);
                    z-index: 2147483647;
                    pointer-events: none;
                    display: none;
                    clip-path: polygon(0% 0%, 0% 100%, 100% 100%, 100% 0%);
                "></div>
            `);

            // Check if dark mode is active
            const isDarkMode = $('.map-accessibility-widget').hasClass('map-dark-mode');

            // Set colors based on dark mode - Purple for dark mode, Blue for light mode
            const borderColor = isDarkMode ? '#7c3aed' : '#6366f1';
            const glowColor = isDarkMode ? '124, 58, 237' : '99, 102, 241';

            // Create premium focus border element with enhanced ADHD glow
            $('body').append(`
                <div id="map-adhd-focus-border" style="
                    position: fixed;
                    left: 0;
                    width: 100%;
                    height: 125px;
                    border-top: 3px solid ${borderColor};
                    border-bottom: 3px solid ${borderColor};
                    border-left: none;
                    border-right: none;
                    box-shadow:
                        0 -15px 30px rgba(${glowColor}, 0.6),
                        0 15px 30px rgba(${glowColor}, 0.6),
                        0 -25px 50px rgba(${glowColor}, 0.4),
                        0 25px 50px rgba(${glowColor}, 0.4),
                        0 -35px 70px rgba(${glowColor}, 0.25),
                        0 35px 70px rgba(${glowColor}, 0.25),
                        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
                    z-index: 2147483648;
                    pointer-events: none;
                    display: none;
                    background: transparent;
                    transition: none;
                "></div>
            `);

            // Add mouse tracking for focus spotlight
            $(document).off('mousemove.adhd').on('mousemove.adhd', (e) => {
                if (this.adhdFocusMode) {
                    this.updateADHDFocusSpotlight(e.clientX, e.clientY);
                }
            });

            // Show initial overlay and border
            $('#map-adhd-focus-overlay, #map-adhd-focus-border').show();
        }

        /**
         * Update ADHD Focus colors when dark mode changes
         */
        updateADHDFocusColors() {
            const $border = $('#map-adhd-focus-border');
            if ($border.length && this.adhdFocusMode) {
                const isDarkMode = $('.map-accessibility-widget').hasClass('map-dark-mode');

                // Set colors: Purple for dark mode, Blue for light mode
                const borderColor = isDarkMode ? '#7c3aed' : '#6366f1';
                const glowColor = isDarkMode ? '124, 58, 237' : '99, 102, 241';

                console.log('Updating ADHD Focus colors:', { isDarkMode, borderColor, glowColor });

                // Update border colors with proper CSS
                $border.css({
                    'border-top': `3px solid ${borderColor}`,
                    'border-bottom': `3px solid ${borderColor}`,
                    'border-top-color': borderColor,
                    'border-bottom-color': borderColor,
                    'box-shadow': `
                        0 -15px 30px rgba(${glowColor}, 0.6),
                        0 15px 30px rgba(${glowColor}, 0.6),
                        0 -25px 50px rgba(${glowColor}, 0.4),
                        0 25px 50px rgba(${glowColor}, 0.4),
                        0 -35px 70px rgba(${glowColor}, 0.25),
                        0 35px 70px rgba(${glowColor}, 0.25),
                        inset 0 0 0 1px rgba(255, 255, 255, 0.1)
                    `
                });
            }
        }

        /**
         * Toggle Big Cursor Mode
         */
        toggleBigCursor() {
            const $toggle = $('#map-big-cursor-toggle');
            const $status = $('#map-big-cursor-status');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Disable Big Cursor Mode
                $toggle.attr('data-active', 'false').removeClass('active');
                $('body').removeClass('map-big-cursor-mode');
                $status.hide();
                this.bigCursorMode = false;
            } else {
                // Enable Big Cursor Mode
                $toggle.attr('data-active', 'true').addClass('active');
                $('body').addClass('map-big-cursor-mode');
                $status.show();
                this.bigCursorMode = true;
            }

            // Save preference
            this.saveUserPreferences();
        }

        /**
         * Toggle Text Magnification Mode
         */
        toggleTextMagnification() {
            const $toggle = $('#map-text-magnification-toggle');
            const $status = $('#map-text-magnification-status');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Disable Text Magnification Mode
                $toggle.attr('data-active', 'false').removeClass('active');
                this.disableTextMagnification();
                $status.hide();
                this.textMagnificationMode = false;
            } else {
                // Enable Text Magnification Mode
                $toggle.attr('data-active', 'true').addClass('active');
                this.enableTextMagnification();
                $status.show();
                this.textMagnificationMode = true;
            }

            // Save preference
            this.saveUserPreferences();
        }

        /**
         * Enable text magnification mode
         */
        enableTextMagnification() {
            // Add body class for CSS targeting
            $('body').addClass('map-text-magnification-mode');

            // Create magnification window if it doesn't exist
            if (!$('#map-magnification-window').length) {
                $('body').append(`
                    <div id="map-magnification-window" class="map-magnification-window" style="display: none;">
                        <div class="map-magnification-content"></div>
                    </div>
                `);
            }

            // Throttle function for performance
            let magnificationTimeout;
            const throttleDelay = 16; // ~60fps

            // Add event listeners for text elements (exclude accessibility widget)
            const textElements = 'p, span, a, h1, h2, h3, h4, h5, h6, li, td, th, div, label, button, input[type="text"], input[type="email"], textarea';

            $(document).off('mouseenter.textMagnification mouseleave.textMagnification')
                .on('mouseenter.textMagnification', textElements, (e) => {
                    if (!this.textMagnificationMode) return;

                    // Skip if element is inside accessibility widget
                    if ($(e.target).closest('.map-widget-panel, .map-main-toggle, .map-magnification-window').length > 0) {
                        return;
                    }

                    clearTimeout(magnificationTimeout);
                    magnificationTimeout = setTimeout(() => {
                        this.showMagnificationWindow(e);
                    }, throttleDelay);
                })
                .on('mouseleave.textMagnification', textElements, () => {
                    if (!this.textMagnificationMode) return;

                    clearTimeout(magnificationTimeout);
                    this.hideMagnificationWindow();
                });

            // Hide magnification window when scrolling
            $(window).off('scroll.textMagnification').on('scroll.textMagnification', () => {
                if (this.textMagnificationMode) {
                    this.hideMagnificationWindow();
                }
            });
        }

        /**
         * Disable text magnification mode
         */
        disableTextMagnification() {
            // Remove body class
            $('body').removeClass('map-text-magnification-mode');

            // Remove event listeners
            $(document).off('mouseenter.textMagnification mouseleave.textMagnification');
            $(window).off('scroll.textMagnification');

            // Hide and remove magnification window
            this.hideMagnificationWindow();
            $('#map-magnification-window').remove();
        }

        /**
         * Show magnification window with text content
         */
        showMagnificationWindow(e) {
            const $target = $(e.target);
            const $window = $('#map-magnification-window');
            const $content = $window.find('.map-magnification-content');

            // Get text content from the target element
            let textContent = $target.text().trim();

            // If no direct text, try to get from closest text-containing element
            if (!textContent || textContent.length < 3) {
                const $textParent = $target.closest('p, span, a, h1, h2, h3, h4, h5, h6, li, td, th, div, label, button').first();
                textContent = $textParent.text().trim();
            }

            // Skip if no meaningful text content
            if (!textContent || textContent.length < 3) {
                return;
            }

            // Limit text length for better UX
            if (textContent.length > 150) {
                textContent = textContent.substring(0, 150) + '...';
            }

            // Set content
            $content.text(textContent);

            // Position the magnification window
            this.positionMagnificationWindow(e, $window);

            // Show the window with smooth animation
            $window.stop(true, true).fadeIn(200);
        }

        /**
         * Hide magnification window
         */
        hideMagnificationWindow() {
            const $window = $('#map-magnification-window');
            $window.stop(true, true).fadeOut(150);
        }

        /**
         * Position magnification window relative to cursor
         */
        positionMagnificationWindow(e, $window) {
            const mouseX = e.pageX;
            const mouseY = e.pageY;
            const windowWidth = $(window).width();
            const windowHeight = $(window).height();
            const scrollTop = $(window).scrollTop();
            const scrollLeft = $(window).scrollLeft();

            // Get magnification window dimensions
            $window.css({ visibility: 'hidden', display: 'block' });
            const magWidth = $window.outerWidth();
            const magHeight = $window.outerHeight();
            $window.css({ visibility: 'visible', display: 'none' });

            // Calculate position with boundary detection
            let left = mouseX + 15; // Offset from cursor
            let top = mouseY - magHeight - 15; // Above cursor

            // Adjust if window goes off-screen horizontally
            if (left + magWidth > scrollLeft + windowWidth) {
                left = mouseX - magWidth - 15; // Move to left of cursor
            }

            // Adjust if window goes off-screen vertically
            if (top < scrollTop) {
                top = mouseY + 15; // Move below cursor
            }

            // Ensure minimum margins
            left = Math.max(scrollLeft + 10, left);
            top = Math.max(scrollTop + 10, top);

            // Apply position
            $window.css({
                left: left + 'px',
                top: top + 'px'
            });
        }

        /**
         * Update ADHD Focus Mode spotlight position
         */
        updateADHDFocusSpotlight(mouseX, mouseY) {
            const $overlay = $('#map-adhd-focus-overlay');
            const $border = $('#map-adhd-focus-border');
            const spotlightHeight = 125; // Height of the focus area
            const windowHeight = window.innerHeight;

            // Calculate the focus area boundaries (mouseY is now clientY - viewport relative)
            const focusTop = Math.max(0, mouseY - spotlightHeight / 2);
            const focusBottom = Math.min(windowHeight, mouseY + spotlightHeight / 2);

            // Convert to percentages for clip-path
            const topPercent = (focusTop / windowHeight) * 100;
            const bottomPercent = (focusBottom / windowHeight) * 100;

            // Create clip-path that excludes the focus area (creates a "hole")
            const clipPath = `polygon(
                0% 0%,
                0% ${topPercent}%,
                100% ${topPercent}%,
                100% 0%,
                0% 0%,
                0% ${bottomPercent}%,
                100% ${bottomPercent}%,
                100% 100%,
                0% 100%,
                0% ${bottomPercent}%
            )`;

            $overlay.css('clip-path', clipPath);

            // Position the premium focus border at the exact boundaries of the focus area
            // Adjust position so borders align with the overlay edges
            const borderWidth = 3; // Border width in pixels
            $border.css({
                'top': (focusTop - borderWidth) + 'px',
                'height': (focusBottom - focusTop) + 'px'
            });
        }

        /**
         * Initialize preferences section features
         */
        initializePreferencesSection() {
            // Dark mode toggle
            $('#map-dark-mode-toggle').off('click.preferences').on('click.preferences', (e) => {
                e.preventDefault();
                this.toggleDarkMode();
            });
        }

        /**
         * Toggle dark mode for the accessibility widget interface
         */
        toggleDarkMode() {
            const $toggle = $('#map-dark-mode-toggle');
            const $widget = $('#map-accessibility-widget');
            const $status = $('#map-dark-mode-status');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Disable dark mode
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                $widget.removeClass('map-dark-mode');
                $status.find('.map-status-text').text('Light interface theme restored');
                this.isDarkModeActive = false;
            } else {
                // Enable dark mode
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                $widget.addClass('map-dark-mode');
                $status.find('.map-status-text').text('Dark interface theme applied!');
                this.isDarkModeActive = true;
            }

            // Show status message
            $status.show().delay(2000).fadeOut();

            // Update ADHD Focus colors if active
            this.updateADHDFocusColors();

            // Save preference
            this.saveUserPreferences();
        }

        /**
         * Restore dark mode state
         */
        restoreDarkModeState() {
            const $toggle = $('#map-dark-mode-toggle');
            const $widget = $('#map-accessibility-widget');

            if (this.isDarkModeActive) {
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                $widget.addClass('map-dark-mode');
            }
        }










        /**
         * Reset all internal states
         */
        resetAllInternalStates() {
            // Reset all properties to defaults
            this.currentFontSize = 100;
            this.currentLineSpacing = 1.5;
            this.currentContrastTheme = 'normal';
            this.isCustomThemeActive = false;
            this.customThemeColors = {
                text: '',
                background: '',
                link: '',
                heading: ''
            };
            this.isDyslexicFontActive = false;
            this.isReadingGuideActive = false;
            this.isTextToSpeechActive = false;
            this.adhdFocusMode = false;
            this.bigCursorMode = false;
            this.textMagnificationMode = false;
            this.isDarkModeActive = false;


            // Navigate theme selector to Default for better UX
            this.navigateToDefaultTheme();
        }

        /**
         * Show reset all message
         */
        showResetAllMessage() {
            let $message = $('#map-reset-all-message');

            if (!$message.length) {
                $message = $('<div id="map-reset-all-message" class="map-status-message" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10000; background: #10b981; color: white; padding: 20px 30px; border-radius: 12px; box-shadow: 0 8px 24px rgba(0,0,0,0.2); font-size: 16px; font-weight: 500; text-align: center;"><div class="map-status-icon" style="font-size: 24px; margin-bottom: 8px;">🔄</div><div class="map-status-text">All settings reset successfully!<br><small>Page will reload in 2 seconds...</small></div></div>');
                $('body').append($message);
            }

            $message.fadeIn(300);
        }

        /**
         * Show reset confirmation message
         */
        showResetConfirmation() {
            // Create or show a temporary confirmation message
            let $confirmation = $('#map-reset-confirmation');
            if (!$confirmation.length) {
                $confirmation = $('<div id="map-reset-confirmation" class="map-reset-confirmation">✓ Category options have been reset to default</div>');
                $('.map-panel-content').prepend($confirmation);
            }

            $confirmation.show().addClass('map-reset-confirmation-show');

            // Hide after 3 seconds
            setTimeout(() => {
                $confirmation.removeClass('map-reset-confirmation-show');
                setTimeout(() => {
                    $confirmation.hide();
                }, 300);
            }, 3000);
        }

        /**
         * Toggle text selection mode for TTS
         */
        toggleTextSelection() {
            const $toggle = $('#map-tts-toggle');
            const isActive = $toggle.attr('data-active') === 'true';

            if (isActive) {
                // Disable text selection mode
                $toggle.attr('data-active', 'false');
                $toggle.removeClass('active');
                this.disableTextSelection();
                this.hideFloatingButton();
                this.stopSpeech();
                this.isTextToSpeechActive = false;
            } else {
                // Enable text selection mode
                $toggle.attr('data-active', 'true');
                $toggle.addClass('active');
                this.enableTextSelection();
                // Play welcome message (only when manually enabled by user)
                this.playWelcomeMessage();
                this.isTextToSpeechActive = true;
            }

            // Save the state to localStorage
            this.saveUserPreferences();
        }

        /**
         * Enable text selection listening
         */
        enableTextSelection() {
            const self = this;

            // Remove existing listeners to avoid duplicates
            $(document).off('mouseup.mapTTS keyup.mapTTS click.mapTTS');

            // Add text selection listeners
            $(document).on('mouseup.mapTTS keyup.mapTTS', function() {
                setTimeout(() => {
                    self.handleTextSelection();
                }, 100);
            });

            // Hide floating button when clicking elsewhere (only if no text is selected)
            $(document).on('click.mapTTS', function(e) {
                if (!$(e.target).closest('#map-floating-play-button').length &&
                    !$(e.target).closest('.map-accessibility-widget').length) {
                    // Small delay to allow selection to be processed
                    setTimeout(() => {
                        const currentSelection = window.getSelection().toString().trim();
                        if (!currentSelection) {
                            self.hideFloatingButton();
                        }
                    }, 100);
                }
            });
        }

        /**
         * Disable text selection listening
         */
        disableTextSelection() {
            $(document).off('mouseup.mapTTS keyup.mapTTS click.mapTTS');
        }

        /**
         * Handle text selection and show floating button
         */
        handleTextSelection() {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();

            if (selectedText.length > 0) {
                // Check if we already have a floating button for different text
                const existingButton = $('#map-floating-play-button');
                const existingText = existingButton.data('selected-text');

                // Only stop speech and recreate button if text has changed
                if (existingText !== selectedText) {
                    // Only stop speech if it's from the floating button (selected text speech)
                    if (this.currentUtterance && existingButton.length) {
                        this.stopFloatingButtonSpeech();
                    }

                    // Show floating play button for new text
                    this.showFloatingButton(selection, selectedText);
                }
            } else {
                // Hide floating button if no text selected
                this.hideFloatingButton();
            }
        }

        /**
         * Speak selected text
         */
        speakSelectedText(text) {
            if (!text || !this.speechSynthesis) return;

            // Prevent multiple simultaneous speech attempts
            if (this.isSpeechInProgress) {

                return;
            }

            this.isSpeechInProgress = true;

            // Stop any existing speech first to avoid conflicts
            if (this.currentUtterance) {
                this.speechSynthesis.cancel();
                this.currentUtterance = null;
            }

            // Show loading state immediately
            this.setFloatingButtonLoading();

            // Longer delay to ensure previous speech is fully cancelled and browser is ready
            setTimeout(() => {
                // Double-check that speech synthesis is ready
                if (this.speechSynthesis.speaking || this.speechSynthesis.pending) {

                    this.speechSynthesis.cancel();

                    // Wait a bit more
                    setTimeout(() => {
                        this.startSpeechSynthesis(text);
                    }, 200);
                } else {
                    this.startSpeechSynthesis(text);
                }
            }, 150);
        }

        /**
         * Start speech synthesis with proper error handling
         */
        startSpeechSynthesis(text) {
            this.currentUtterance = new SpeechSynthesisUtterance(text);
            this.currentUtterance.rate = 1.0;
            this.currentUtterance.pitch = 1.0;
            this.currentUtterance.volume = 1.0;

            const self = this;

            // Fallback timer to ensure button restoration if events fail
            const fallbackTimer = setTimeout(() => {
                if (self.currentUtterance) {

                    self.setFloatingButtonPlay();
                    self.isSpeechInProgress = false;
                }
            }, 10000); // 10 second fallback

            // When speech starts, change to stop button
            this.currentUtterance.onstart = function() {
                self.setFloatingButtonStop();
            };

            this.currentUtterance.onend = function() {
                clearTimeout(fallbackTimer);
                self.currentUtterance = null;
                self.isSpeechInProgress = false;
                self.setFloatingButtonPlay();
                // Keep button visible - don't auto-hide after speech ends
            };

            this.currentUtterance.onerror = function(event) {
                clearTimeout(fallbackTimer);
                self.currentUtterance = null;
                self.isSpeechInProgress = false;

                // Only log error if it's not an 'interrupted' error (which is expected)
                if (event.error !== 'interrupted') {

                }

                self.setFloatingButtonPlay();
                // Keep button visible even after error
            };

            // Additional fallback for browsers that don't fire onstart
            setTimeout(() => {
                if (self.currentUtterance && $('#map-floating-play-button .map-loading').length) {
                    self.setFloatingButtonStop();
                }
            }, 500);

            try {
                this.speechSynthesis.speak(this.currentUtterance);
            } catch (error) {

                this.isSpeechInProgress = false;
                this.setFloatingButtonPlay();
            }
        }

        /**
         * Play welcome message when TTS is activated
         */
        playWelcomeMessage() {
            const welcomeText = "Select text and click play to listen";

            if (this.speechSynthesis) {
                const utterance = new SpeechSynthesisUtterance(welcomeText);
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                utterance.volume = 0.8;

                this.speechSynthesis.speak(utterance);
            }
        }

        /**
         * Show floating play button near text selection
         */
        showFloatingButton(selection, selectedText) {
            // Remove existing button
            this.hideFloatingButton();

            // Get selection position
            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();

            // Create floating button
            const button = $(`
                <div id="map-floating-play-button" class="map-floating-play-button">
                    <button type="button" class="map-play-btn" aria-label="Play selected text">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                    </button>
                </div>
            `);

            // Calculate position with viewport boundaries
            const scrollTop = $(window).scrollTop();
            const scrollLeft = $(window).scrollLeft();
            const windowWidth = $(window).width();
            const windowHeight = $(window).height();

            let top = rect.top + scrollTop - 45;
            let left = rect.left + scrollLeft + (rect.width / 2) - 20;

            // Ensure button stays within viewport
            if (left < 10) left = 10;
            if (left > windowWidth - 50) left = windowWidth - 50;
            if (top < scrollTop + 10) top = rect.bottom + scrollTop + 5;

            button.css({
                position: 'absolute',
                top: top,
                left: left,
                zIndex: 999999
            });

            // Store selected text
            button.data('selected-text', selectedText);

            // Add click handler
            const self = this;
            button.find('.map-play-btn').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const textToSpeak = button.data('selected-text');
                self.speakSelectedText(textToSpeak);
            });

            // Add to body
            $('body').append(button);

            // Store auto-hide timer ID for potential cancellation
            const autoHideTimer = setTimeout(() => {
                // Only hide if text is no longer selected
                const currentSelection = window.getSelection().toString().trim();
                if (!currentSelection || currentSelection !== selectedText) {
                    this.hideFloatingButton();
                }
            }, 10000); // Increased to 10 seconds

            // Store timer ID on button for potential cancellation
            button.data('auto-hide-timer', autoHideTimer);
        }

        /**
         * Hide floating play button
         */
        hideFloatingButton() {
            const $button = $('#map-floating-play-button');
            if ($button.length) {
                // Cancel auto-hide timer if it exists
                const autoHideTimer = $button.data('auto-hide-timer');
                if (autoHideTimer) {
                    clearTimeout(autoHideTimer);
                }
                $button.remove();
            }
        }

        /**
         * Set floating button to loading state
         */
        setFloatingButtonLoading() {
            const $button = $('#map-floating-play-button .map-play-btn');
            if ($button.length) {
                $button.addClass('map-loading');
                $button.find('svg').hide();
                $button.append('<div class="map-loading-spinner"></div>');

                // Disable button during loading to prevent multiple clicks
                $button.prop('disabled', true);
            }
        }

        /**
         * Set floating button to stop state
         */
        setFloatingButtonStop() {
            const $button = $('#map-floating-play-button .map-play-btn');
            if ($button.length) {
                // Remove loading state
                $button.removeClass('map-loading');
                $button.find('.map-loading-spinner').remove();
                $button.prop('disabled', false);

                // Change to stop state
                $button.addClass('map-stop-btn');
                $button.attr('aria-label', 'Stop reading');

                // Replace icon with stop icon
                $button.find('svg').show().replaceWith(`
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6 6h12v12H6z"/>
                    </svg>
                `);

                // Update click handler for stop functionality
                const self = this;
                $button.off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    self.stopFloatingButtonSpeech();
                });
            }
        }

        /**
         * Set floating button to play state
         */
        setFloatingButtonPlay() {
            const $button = $('#map-floating-play-button .map-play-btn');
            if ($button.length) {
                // Remove stop state and loading state
                $button.removeClass('map-stop-btn map-loading');
                $button.find('.map-loading-spinner').remove();
                $button.prop('disabled', false);
                $button.attr('aria-label', 'Play selected text');

                // Replace icon with play icon
                $button.find('svg').show().replaceWith(`
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                `);

                // Restore original click handler
                const self = this;
                const selectedText = $('#map-floating-play-button').data('selected-text');
                if (selectedText) {
                    $button.off('click').on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        self.speakSelectedText(selectedText);
                    });
                }
            }
        }

        /**
         * Stop speech from floating button
         */
        stopFloatingButtonSpeech() {
            // Cancel any ongoing speech
            if (this.speechSynthesis.speaking || this.speechSynthesis.paused) {
                this.speechSynthesis.cancel();
            }

            // Clear current utterance and reset flags
            this.currentUtterance = null;
            this.isSpeechInProgress = false;

            // Small delay to ensure cancellation is processed
            setTimeout(() => {
                // Restore button to play state
                this.setFloatingButtonPlay();
            }, 100); // Increased delay to ensure proper cancellation
        }

        /**
         * Toggle speech synthesis (legacy method for compatibility)
         */
        toggleSpeech() {
            if (this.isPlaying && !this.isPaused) {
                this.pauseSpeech();
            } else if (this.isPaused) {
                this.resumeSpeech();
            } else {
                this.startSpeech();
            }
        }

        /**
         * Start speech synthesis
         */
        startSpeech() {
            const text = this.extractPageText();

            if (!text.trim()) {
                this.showMessage(mapAjax.strings.error || 'No text found to read');
                return;
            }

            this.currentText = text;
            this.currentUtterance = new SpeechSynthesisUtterance(text);

            // Configure utterance
            this.currentUtterance.rate = parseFloat($('#map-speed-control').val()) || 1.0;
            this.currentUtterance.pitch = 1.0;
            this.currentUtterance.volume = 1.0;

            // Event handlers
            this.currentUtterance.onstart = () => {
                this.isPlaying = true;
                this.isPaused = false;
                this.updateUI();
                this.startProgress();
            };

            this.currentUtterance.onend = () => {
                this.isPlaying = false;
                this.isPaused = false;
                this.currentPosition = 0;
                this.updateUI();
                this.clearHighlight();
                this.hideProgress();
            };

            this.currentUtterance.onerror = (event) => {

                this.stopSpeech();
                this.showMessage(mapAjax.strings.error || 'Speech synthesis error');
            };

            this.currentUtterance.onboundary = (event) => {
                if (this.highlightEnabled) {
                    this.highlightText(event.charIndex, event.charLength);
                }
            };

            // Start speaking
            this.speechSynthesis.speak(this.currentUtterance);
        }

        /**
         * Pause speech synthesis
         */
        pauseSpeech() {
            if (this.speechSynthesis.speaking) {
                this.speechSynthesis.pause();
                this.isPaused = true;
                this.updateUI();
            }
        }

        /**
         * Resume speech synthesis
         */
        resumeSpeech() {
            if (this.speechSynthesis.paused) {
                this.speechSynthesis.resume();
                this.isPaused = false;
                this.updateUI();
            }
        }

        /**
         * Stop speech synthesis
         */
        stopSpeech() {
            if (this.speechSynthesis.speaking || this.speechSynthesis.paused) {
                this.speechSynthesis.cancel();
            }

            this.isPlaying = false;
            this.isPaused = false;
            this.currentPosition = 0;
            this.currentUtterance = null;

            // Restore floating button to play state if it exists
            if ($('#map-floating-play-button').length) {
                this.setFloatingButtonPlay();
            }

            this.updateUI();
            this.clearHighlight();
            this.hideProgress();
        }

        /**
         * Update speech rate
         */
        updateSpeechRate(rate) {
            if (this.currentUtterance) {
                // For ongoing speech, we need to restart with new rate
                if (this.isPlaying) {
                    const wasPlaying = !this.isPaused;
                    this.stopSpeech();

                    if (wasPlaying) {
                        setTimeout(() => {
                            this.startSpeech();
                        }, 100);
                    }
                }
            }
        }

        /**
         * Extract text from page content
         */
        extractPageText() {
            let text = '';

            // Get main content
            const contentSelectors = [
                '[data-map-content="true"]',
                'main',
                '.entry-content',
                '.post-content',
                '.content',
                'article',
                '.single-post'
            ];

            let $content = null;
            for (const selector of contentSelectors) {
                $content = $(selector).first();
                if ($content.length) break;
            }

            if (!$content || !$content.length) {
                $content = $('body');
            }

            // Extract text, excluding certain elements
            const $clone = $content.clone();
            $clone.find('script, style, nav, header, footer, .map-accessibility-widget').remove();

            text = $clone.text().replace(/\s+/g, ' ').trim();

            return text;
        }

        /**
         * Update UI elements
         */
        updateUI() {
            const $toggleBtn = $('#map-tts-toggle');
            const $stopBtn = $('#map-tts-stop');
            const $playIcon = $toggleBtn.find('.map-play-icon');
            const $pauseIcon = $toggleBtn.find('.map-pause-icon');
            const $buttonText = $toggleBtn.find('.map-button-text');

            if (this.isPlaying && !this.isPaused) {
                $toggleBtn.attr('aria-pressed', 'true');
                $playIcon.hide();
                $pauseIcon.show();
                $buttonText.text(mapAjax.strings.pause || 'Pause');
                $stopBtn.prop('disabled', false);
            } else if (this.isPaused) {
                $toggleBtn.attr('aria-pressed', 'false');
                $playIcon.show();
                $pauseIcon.hide();
                $buttonText.text(mapAjax.strings.play || 'Resume');
                $stopBtn.prop('disabled', false);
            } else {
                $toggleBtn.attr('aria-pressed', 'false');
                $playIcon.show();
                $pauseIcon.hide();
                $buttonText.text(mapAjax.strings.play || 'Start Reading');
                $stopBtn.prop('disabled', true);
            }
        }

        /**
         * Show progress bar
         */
        startProgress() {
            $('.map-progress-container').show();
            // Note: Actual progress tracking would require more complex implementation
            // This is a simplified version
        }

        /**
         * Hide progress bar
         */
        hideProgress() {
            $('.map-progress-container').hide();
            $('#map-progress-fill').css('width', '0%');
            $('.map-progress-text').text('0%');
        }

        /**
         * Highlight text being read
         */
        highlightText(charIndex, charLength) {
            // This is a simplified implementation
            // A full implementation would require more sophisticated text mapping
            this.clearHighlight();

            // Add highlight class to current element being read
            // This would need more complex implementation for accurate highlighting
        }

        /**
         * Clear text highlighting
         */
        clearHighlight() {
            $('.map-highlight-text').removeClass('map-highlight-text');
        }

        /**
         * Load available voices
         */
        loadVoices() {
            const voices = this.speechSynthesis.getVoices();
            const $voiceSelect = $('#map-voice-select');

            if ($voiceSelect.length && voices.length) {
                $voiceSelect.empty().append('<option value="">Default Voice</option>');

                voices.forEach((voice, index) => {
                    const option = $('<option></option>')
                        .val(index)
                        .text(`${voice.name} (${voice.lang})`);
                    $voiceSelect.append(option);
                });

                $voiceSelect.parent().show();
            }
        }

        /**
         * Show message to user
         */
        showMessage(message) {
            // Simple alert for now - could be enhanced with custom notifications
            alert(message);
        }

        /**
         * Clear all user preferences (for reset functionality)
         */
        clearUserPreferences() {
            try {
                localStorage.removeItem('map_tts_active');
                localStorage.removeItem('map_speech_rate');
                localStorage.removeItem('map_highlight_enabled');
                localStorage.removeItem('map_autoscroll_enabled');
                localStorage.removeItem('map_dyslexic_font_active');
                localStorage.removeItem('map_reading_guide_active');
                localStorage.removeItem('map_adhd_focus_mode');
                localStorage.removeItem('map_big_cursor_mode');
                localStorage.removeItem('map_text_magnification_mode');
                localStorage.removeItem('map_font_size');
                localStorage.removeItem('map_line_spacing');
                localStorage.removeItem('map_contrast_theme');
                localStorage.removeItem('map_custom_theme_colors');
                localStorage.removeItem('map_custom_theme_active');

            } catch (e) {

            }
        }

        /**
         * Destroy the accessibility instance and clean up
         */
        destroy() {
            // Stop any ongoing speech
            if (this.currentUtterance) {
                this.speechSynthesis.cancel();
                this.currentUtterance = null;
            }

            // Remove event listeners
            $(document).off('.mapTTS');
            $(document).off('.mapAccessibility');
            $(window).off('.mapAccessibility');

            // Remove floating button if it exists
            $('#map-floating-play-button').remove();

            // Clear any timeouts/intervals
            this.clearHighlight();

            // Reset flags
            this.isPlaying = false;
            this.isPaused = false;
            this.isSpeechInProgress = false;
            this.isTextToSpeechActive = false;
            this.isDyslexicFontActive = false;
            this.isReadingGuideActive = false;
            this.currentFontSize = 100;
            this.currentLineSpacing = 1.5;
            this.currentText = '';
            this.currentPosition = 0;

            // Clean up dyslexic font if active
            this.disableDyslexicFont();

            // Clean up reading guide if active
            this.disableReadingGuide();

            // Reset font size to default
            $('#map-font-size-style').remove();
            $('html').removeClass('map-font-size-active');

            // Reset line spacing to default
            $('#map-line-spacing-style').remove();
            $('html').removeClass('map-line-spacing-active');


        }

        /**
         * Toggle navigation arrow rotation for expandable sections
         * @param {jQuery} $toggle - The toggle button element
         */
        toggleNavigationArrow($toggle) {
            // Use a small delay to ensure the data-active attribute is updated first
            setTimeout(() => {
                const $arrow = $toggle.find('.map-category-arrow');
                if ($arrow.length) {
                    const isActive = $toggle.attr('data-active') === 'true';

                    if (isActive) {
                        // Rotate to down arrow (∨)
                        $arrow.css('transform', 'rotate(90deg)');
                    } else {
                        // Reset to right arrow (>)
                        $arrow.css('transform', 'rotate(0deg)');
                    }
                }
            }, 10);
        }

        /**
         * Toggle reset button visibility based on color value
         * @param {jQuery} $colorPicker - The color picker element
         */
        toggleResetButtonVisibility($colorPicker) {
            const colorValue = $colorPicker.val();
            const pickerId = $colorPicker.attr('id');
            const $resetBtn = $(`[data-target="${pickerId}"]`);

            if ($resetBtn.length) {
                // Show reset button if color has been changed from default (empty or #000000)
                if (colorValue && colorValue !== '#000000' && colorValue !== '') {
                    $resetBtn.addClass('visible').show();
                } else {
                    $resetBtn.removeClass('visible').hide();
                }
            }
        }
    }

    // Global instance to prevent multiple initializations
    let mapAccessibilityInstance = null;

    /**
     * Initialize or reinitialize the accessibility plugin
     */
    function initializeAccessibility() {
        // Check if widget exists (mapAjax is optional for basic functionality)
        if ($('#map-accessibility-widget').length) {
            // Destroy existing instance if it exists
            if (mapAccessibilityInstance) {
                mapAccessibilityInstance.destroy();
            }

            // Create new instance
            mapAccessibilityInstance = new MapAccessibility();

            // Ensure proper initial state
            const mainMenu = $('#map-main-menu');
            if (mainMenu.length) {
                $('.map-modal-view').removeClass('map-modal-view-active');
                mainMenu.addClass('map-modal-view-active');
            }
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initializeAccessibility();
    });

    // Reinitialize on page changes (for AJAX navigation, SPA, etc.)
    $(window).on('load', function() {
        initializeAccessibility();
    });

    // Handle browser back/forward navigation
    $(window).on('popstate', function() {
        setTimeout(initializeAccessibility, 100);
    });

    // Handle potential AJAX page loads (common in modern themes)
    $(document).on('DOMContentLoaded', function() {
        initializeAccessibility();
    });

    // Handle various page change events
    $(window).on('hashchange', function() {
        setTimeout(initializeAccessibility, 200);
    });

    // Handle potential theme/plugin AJAX navigation
    $(document).on('pjax:complete', function() {
        setTimeout(initializeAccessibility, 100);
    });

    // Handle Turbo/Turbolinks navigation (if used)
    $(document).on('turbo:load turbolinks:load', function() {
        setTimeout(initializeAccessibility, 100);
    });

    // Periodic check to ensure widget persistence (every 3 seconds)
    setInterval(function() {
        // Only check if we should have an instance but don't
        if (!mapAccessibilityInstance) {
            // Check if widget appeared in DOM
            if ($('#map-accessibility-widget').length) {

                initializeAccessibility();
            }
        }
        // Also check if instance exists but widget is gone
        else if (mapAccessibilityInstance && !$('#map-accessibility-widget').length) {

            mapAccessibilityInstance.destroy();
            mapAccessibilityInstance = null;
        }
    }, 3000); // Check every 3 seconds for faster recovery

    // MutationObserver to detect when the widget is added/removed from DOM
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // Check if accessibility widget was added
                if (mutation.type === 'childList') {
                    const addedNodes = Array.from(mutation.addedNodes);
                    const removedNodes = Array.from(mutation.removedNodes);

                    // Widget was added
                    if (addedNodes.some(node =>
                        node.nodeType === 1 &&
                        (node.id === 'map-accessibility-widget' ||
                         node.querySelector && node.querySelector('#map-accessibility-widget'))
                    )) {
                        setTimeout(initializeAccessibility, 50);
                    }

                    // Widget was removed - clean up
                    if (removedNodes.some(node =>
                        node.nodeType === 1 &&
                        (node.id === 'map-accessibility-widget' ||
                         node.querySelector && node.querySelector('#map-accessibility-widget'))
                    )) {
                        if (mapAccessibilityInstance) {
                            mapAccessibilityInstance.destroy();
                            mapAccessibilityInstance = null;
                        }
                    }
                }
            });
        });

        // Start observing
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

})(jQuery);
