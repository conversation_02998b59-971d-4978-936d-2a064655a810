<?php
/**
 * Admin settings page class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin Settings class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Admin_Settings {

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        add_action('admin_init', array($this, 'init_settings'));
    }

    /**
     * Initialize settings
     *
     * @since 1.0.0
     */
    public function init_settings() {
        register_setting(
            'map_settings_group',
            'map_settings',
            array($this, 'sanitize_settings')
        );

        // General Settings Section
        add_settings_section(
            'map_general_section',
            __('General Settings', MAP_TEXT_DOMAIN),
            array($this, 'general_section_callback'),
            'my-accessibility-plugin'
        );

        // Text-to-Speech Settings Section
        add_settings_section(
            'map_tts_section',
            __('Text-to-Speech Settings', MAP_TEXT_DOMAIN),
            array($this, 'tts_section_callback'),
            'my-accessibility-plugin'
        );

        // Widget Settings Section
        add_settings_section(
            'map_widget_section',
            __('Widget Settings', MAP_TEXT_DOMAIN),
            array($this, 'widget_section_callback'),
            'my-accessibility-plugin'
        );

        // Add settings fields
        $this->add_settings_fields();
    }

    /**
     * Add settings fields
     *
     * @since 1.0.0
     */
    private function add_settings_fields() {
        // Enable Text-to-Speech
        add_settings_field(
            'text_to_speech_enabled',
            __('Enable Text-to-Speech', MAP_TEXT_DOMAIN),
            array($this, 'checkbox_field_callback'),
            'my-accessibility-plugin',
            'map_general_section',
            array(
                'field' => 'text_to_speech_enabled',
                'description' => __('Enable the text-to-speech functionality on your website.', MAP_TEXT_DOMAIN)
            )
        );

        // Speech Rate
        add_settings_field(
            'speech_rate',
            __('Speech Rate', MAP_TEXT_DOMAIN),
            array($this, 'range_field_callback'),
            'my-accessibility-plugin',
            'map_tts_section',
            array(
                'field' => 'speech_rate',
                'min' => 0.1,
                'max' => 10,
                'step' => 0.1,
                'description' => __('Control the speed of speech synthesis (0.1 = very slow, 10 = very fast).', MAP_TEXT_DOMAIN)
            )
        );

        // Speech Pitch
        add_settings_field(
            'speech_pitch',
            __('Speech Pitch', MAP_TEXT_DOMAIN),
            array($this, 'range_field_callback'),
            'my-accessibility-plugin',
            'map_tts_section',
            array(
                'field' => 'speech_pitch',
                'min' => 0,
                'max' => 2,
                'step' => 0.1,
                'description' => __('Control the pitch of speech synthesis (0 = low, 2 = high).', MAP_TEXT_DOMAIN)
            )
        );

        // Speech Volume
        add_settings_field(
            'speech_volume',
            __('Speech Volume', MAP_TEXT_DOMAIN),
            array($this, 'range_field_callback'),
            'my-accessibility-plugin',
            'map_tts_section',
            array(
                'field' => 'speech_volume',
                'min' => 0,
                'max' => 1,
                'step' => 0.1,
                'description' => __('Control the volume of speech synthesis (0 = mute, 1 = full volume).', MAP_TEXT_DOMAIN)
            )
        );

        // Widget Position
        add_settings_field(
            'widget_position',
            __('Widget Position', MAP_TEXT_DOMAIN),
            array($this, 'select_field_callback'),
            'my-accessibility-plugin',
            'map_widget_section',
            array(
                'field' => 'widget_position',
                'options' => array(
                    'top-left' => __('Top Left', MAP_TEXT_DOMAIN),
                    'top-right' => __('Top Right', MAP_TEXT_DOMAIN),
                    'bottom-left' => __('Bottom Left', MAP_TEXT_DOMAIN),
                    'bottom-right' => __('Bottom Right', MAP_TEXT_DOMAIN),
                    'center' => __('Center', MAP_TEXT_DOMAIN)
                ),
                'description' => __('Choose where to display the accessibility widget on your website.', MAP_TEXT_DOMAIN)
            )
        );

        // Widget Style
        add_settings_field(
            'widget_style',
            __('Widget Style', MAP_TEXT_DOMAIN),
            array($this, 'select_field_callback'),
            'my-accessibility-plugin',
            'map_widget_section',
            array(
                'field' => 'widget_style',
                'options' => array(
                    'modern' => __('Modern', MAP_TEXT_DOMAIN),
                    'classic' => __('Classic', MAP_TEXT_DOMAIN),
                    'minimal' => __('Minimal', MAP_TEXT_DOMAIN)
                ),
                'description' => __('Choose the visual style of the accessibility widget.', MAP_TEXT_DOMAIN)
            )
        );

        // Button Text
        add_settings_field(
            'button_text',
            __('Button Text', MAP_TEXT_DOMAIN),
            array($this, 'text_field_callback'),
            'my-accessibility-plugin',
            'map_widget_section',
            array(
                'field' => 'button_text',
                'description' => __('Customize the text displayed on the accessibility button.', MAP_TEXT_DOMAIN)
            )
        );

        // Button Color
        add_settings_field(
            'button_color',
            __('Button Color', MAP_TEXT_DOMAIN),
            array($this, 'color_field_callback'),
            'my-accessibility-plugin',
            'map_widget_section',
            array(
                'field' => 'button_color',
                'description' => __('Choose the color of the accessibility button.', MAP_TEXT_DOMAIN)
            )
        );

        // Button Size
        add_settings_field(
            'button_size',
            __('Button Size', MAP_TEXT_DOMAIN),
            array($this, 'select_field_callback'),
            'my-accessibility-plugin',
            'map_widget_section',
            array(
                'field' => 'button_size',
                'options' => array(
                    'small' => __('Small', MAP_TEXT_DOMAIN),
                    'medium' => __('Medium', MAP_TEXT_DOMAIN),
                    'large' => __('Large', MAP_TEXT_DOMAIN)
                ),
                'description' => __('Choose the size of the accessibility button.', MAP_TEXT_DOMAIN)
            )
        );
    }

    /**
     * Render settings page
     *
     * @since 1.0.0
     */
    public function render_settings_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', MAP_TEXT_DOMAIN));
        }
        ?>
        <div class="wrap">
            <h1><?php esc_html_e('My Accessibility Plugin Settings', MAP_TEXT_DOMAIN); ?></h1>
            
            <?php if (isset($_GET['import-error'])): ?>
                <div class="notice notice-error is-dismissible">
                    <p><?php esc_html_e('Error importing settings. Please check the file format.', MAP_TEXT_DOMAIN); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="map-admin-container">
                <div class="map-admin-main">
                    <form method="post" action="options.php">
                        <?php
                        settings_fields('map_settings_group');
                        do_settings_sections('my-accessibility-plugin');
                        submit_button();
                        ?>
                    </form>
                </div>
                
                <div class="map-admin-sidebar">
                    <div class="map-admin-box">
                        <h3><?php esc_html_e('Quick Actions', MAP_TEXT_DOMAIN); ?></h3>
                        
                        <form method="post" style="margin-bottom: 10px;">
                            <?php wp_nonce_field('map_reset_settings'); ?>
                            <input type="submit" name="map_reset_settings" class="button" value="<?php esc_attr_e('Reset to Defaults', MAP_TEXT_DOMAIN); ?>" onclick="return confirm('<?php esc_attr_e('Are you sure you want to reset all settings?', MAP_TEXT_DOMAIN); ?>');">
                        </form>
                        
                        <form method="post" style="margin-bottom: 10px;">
                            <?php wp_nonce_field('map_export_settings'); ?>
                            <input type="submit" name="map_export_settings" class="button" value="<?php esc_attr_e('Export Settings', MAP_TEXT_DOMAIN); ?>">
                        </form>
                        
                        <form method="post" enctype="multipart/form-data">
                            <?php wp_nonce_field('map_import_settings'); ?>
                            <input type="file" name="settings_file" accept=".json" required>
                            <input type="submit" name="map_import_settings" class="button" value="<?php esc_attr_e('Import Settings', MAP_TEXT_DOMAIN); ?>">
                        </form>
                    </div>
                    
                    <div class="map-admin-box">
                        <h3><?php esc_html_e('Support', MAP_TEXT_DOMAIN); ?></h3>
                        <p><?php esc_html_e('Need help? Check out our documentation or contact support.', MAP_TEXT_DOMAIN); ?></p>
                        <a href="#" class="button button-secondary" target="_blank"><?php esc_html_e('Documentation', MAP_TEXT_DOMAIN); ?></a>
                        <a href="#" class="button button-secondary" target="_blank"><?php esc_html_e('Support', MAP_TEXT_DOMAIN); ?></a>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * General section callback
     *
     * @since 1.0.0
     */
    public function general_section_callback() {
        echo '<p>' . esc_html__('Configure the general settings for the accessibility plugin.', MAP_TEXT_DOMAIN) . '</p>';
    }

    /**
     * TTS section callback
     *
     * @since 1.0.0
     */
    public function tts_section_callback() {
        echo '<p>' . esc_html__('Configure text-to-speech settings to customize the voice output.', MAP_TEXT_DOMAIN) . '</p>';
    }

    /**
     * Widget section callback
     *
     * @since 1.0.0
     */
    public function widget_section_callback() {
        echo '<p>' . esc_html__('Customize the appearance and behavior of the accessibility widget.', MAP_TEXT_DOMAIN) . '</p>';
    }

    /**
     * Checkbox field callback
     *
     * @param array $args
     * @since 1.0.0
     */
    public function checkbox_field_callback($args) {
        $settings = MAP_Core::get_instance()->get_settings();
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : false;

        printf(
            '<input type="checkbox" id="%1$s" name="map_settings[%1$s]" value="1" %2$s>',
            esc_attr($args['field']),
            checked($value, true, false)
        );

        if (isset($args['description'])) {
            printf('<p class="description">%s</p>', esc_html($args['description']));
        }
    }

    /**
     * Text field callback
     *
     * @param array $args
     * @since 1.0.0
     */
    public function text_field_callback($args) {
        $settings = MAP_Core::get_instance()->get_settings();
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';

        printf(
            '<input type="text" id="%1$s" name="map_settings[%1$s]" value="%2$s" class="regular-text">',
            esc_attr($args['field']),
            esc_attr($value)
        );

        if (isset($args['description'])) {
            printf('<p class="description">%s</p>', esc_html($args['description']));
        }
    }

    /**
     * Range field callback
     *
     * @param array $args
     * @since 1.0.0
     */
    public function range_field_callback($args) {
        $settings = MAP_Core::get_instance()->get_settings();
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : $args['min'];

        printf(
            '<input type="range" id="%1$s" name="map_settings[%1$s]" value="%2$s" min="%3$s" max="%4$s" step="%5$s" class="map-range-slider">',
            esc_attr($args['field']),
            esc_attr($value),
            esc_attr($args['min']),
            esc_attr($args['max']),
            esc_attr($args['step'])
        );

        printf('<span class="map-range-value">%s</span>', esc_html($value));

        if (isset($args['description'])) {
            printf('<p class="description">%s</p>', esc_html($args['description']));
        }
    }

    /**
     * Select field callback
     *
     * @param array $args
     * @since 1.0.0
     */
    public function select_field_callback($args) {
        $settings = MAP_Core::get_instance()->get_settings();
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '';

        printf('<select id="%1$s" name="map_settings[%1$s]">', esc_attr($args['field']));

        foreach ($args['options'] as $option_value => $option_label) {
            printf(
                '<option value="%1$s" %2$s>%3$s</option>',
                esc_attr($option_value),
                selected($value, $option_value, false),
                esc_html($option_label)
            );
        }

        echo '</select>';

        if (isset($args['description'])) {
            printf('<p class="description">%s</p>', esc_html($args['description']));
        }
    }

    /**
     * Color field callback
     *
     * @param array $args
     * @since 1.0.0
     */
    public function color_field_callback($args) {
        $settings = MAP_Core::get_instance()->get_settings();
        $value = isset($settings[$args['field']]) ? $settings[$args['field']] : '#0073aa';

        printf(
            '<input type="color" id="%1$s" name="map_settings[%1$s]" value="%2$s">',
            esc_attr($args['field']),
            esc_attr($value)
        );

        if (isset($args['description'])) {
            printf('<p class="description">%s</p>', esc_html($args['description']));
        }
    }

    /**
     * Sanitize settings
     *
     * @param array $input
     * @return array
     * @since 1.0.0
     */
    public function sanitize_settings($input) {
        $settings_instance = MAP_Core::get_instance()->settings;
        return $settings_instance->validate_settings($input);
    }
}
