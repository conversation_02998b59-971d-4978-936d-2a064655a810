<?php
/**
 * Security helper class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Security class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Security {

    /**
     * Initialize security measures
     *
     * @since 1.0.0
     */
    public static function init() {
        // Add security headers
        add_action('wp_head', array(__CLASS__, 'add_security_headers'));
        
        // Sanitize all inputs
        add_action('init', array(__CLASS__, 'sanitize_inputs'));
        
        // Rate limiting for AJAX requests
        add_action('wp_ajax_map_get_text_content', array(__CLASS__, 'rate_limit_check'), 1);
        add_action('wp_ajax_nopriv_map_get_text_content', array(__CLASS__, 'rate_limit_check'), 1);
        
        // Prevent information disclosure
        add_filter('wp_die_handler', array(__CLASS__, 'custom_die_handler'));
    }

    /**
     * Add security headers
     *
     * @since 1.0.0
     */
    public static function add_security_headers() {
        if (!headers_sent()) {
            // Content Security Policy for plugin assets
            $csp = "script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';";
            header("Content-Security-Policy: " . $csp);
            
            // Prevent clickjacking
            header('X-Frame-Options: SAMEORIGIN');
            
            // Prevent MIME type sniffing
            header('X-Content-Type-Options: nosniff');
            
            // XSS Protection
            header('X-XSS-Protection: 1; mode=block');
        }
    }

    /**
     * Sanitize all inputs
     *
     * @since 1.0.0
     */
    public static function sanitize_inputs() {
        // Sanitize GET parameters
        if (!empty($_GET)) {
            $_GET = self::sanitize_array($_GET);
        }
        
        // Sanitize POST parameters
        if (!empty($_POST)) {
            $_POST = self::sanitize_array($_POST);
        }
    }

    /**
     * Recursively sanitize array
     *
     * @param array $array
     * @return array
     * @since 1.0.0
     */
    private static function sanitize_array($array) {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = self::sanitize_array($value);
            } else {
                // Basic sanitization - more specific sanitization done in individual functions
                $array[$key] = sanitize_text_field($value);
            }
        }
        return $array;
    }

    /**
     * Verify nonce for AJAX requests
     *
     * @param string $nonce
     * @param string $action
     * @return bool
     * @since 1.0.0
     */
    public static function verify_nonce($nonce, $action = 'map_nonce') {
        return wp_verify_nonce($nonce, $action);
    }

    /**
     * Check user capabilities
     *
     * @param string $capability
     * @return bool
     * @since 1.0.0
     */
    public static function check_capability($capability = 'read') {
        return current_user_can($capability);
    }

    /**
     * Rate limiting for AJAX requests
     *
     * @since 1.0.0
     */
    public static function rate_limit_check() {
        $user_ip = self::get_user_ip();
        $transient_key = 'map_rate_limit_' . md5($user_ip);
        
        $requests = get_transient($transient_key);
        
        if ($requests === false) {
            // First request
            set_transient($transient_key, 1, MINUTE_IN_SECONDS);
        } else {
            $requests++;
            
            // Allow max 60 requests per minute
            if ($requests > 60) {
                wp_die(__('Rate limit exceeded. Please try again later.', MAP_TEXT_DOMAIN), 429);
            }
            
            set_transient($transient_key, $requests, MINUTE_IN_SECONDS);
        }
    }

    /**
     * Get user IP address
     *
     * @return string
     * @since 1.0.0
     */
    private static function get_user_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Sanitize text for speech synthesis
     *
     * @param string $text
     * @return string
     * @since 1.0.0
     */
    public static function sanitize_speech_text($text) {
        // Remove HTML tags
        $text = wp_strip_all_tags($text);
        
        // Remove script and style content
        $text = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $text);
        $text = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $text);
        
        // Remove potentially dangerous content
        $text = preg_replace('/javascript:/i', '', $text);
        $text = preg_replace('/vbscript:/i', '', $text);
        $text = preg_replace('/onload/i', '', $text);
        
        // Normalize whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        // Limit length to prevent abuse
        $text = substr($text, 0, 50000); // 50KB limit
        
        return trim($text);
    }

    /**
     * Validate settings input
     *
     * @param array $settings
     * @return array
     * @since 1.0.0
     */
    public static function validate_settings($settings) {
        $validated = array();
        
        // Boolean settings
        $boolean_fields = array('text_to_speech_enabled', 'auto_highlight', 'keyboard_shortcuts');
        foreach ($boolean_fields as $field) {
            $validated[$field] = isset($settings[$field]) ? (bool) $settings[$field] : false;
        }
        
        // String settings with allowed values
        $validated['widget_position'] = in_array($settings['widget_position'], 
            array('top-left', 'top-right', 'bottom-left', 'bottom-right', 'center')) 
            ? $settings['widget_position'] : 'bottom-right';
            
        $validated['widget_style'] = in_array($settings['widget_style'], 
            array('modern', 'classic', 'minimal')) 
            ? $settings['widget_style'] : 'modern';
            
        $validated['button_size'] = in_array($settings['button_size'], 
            array('small', 'medium', 'large')) 
            ? $settings['button_size'] : 'medium';
        
        // Numeric settings with ranges
        $validated['speech_rate'] = max(0.1, min(10, floatval($settings['speech_rate'])));
        $validated['speech_pitch'] = max(0, min(2, floatval($settings['speech_pitch'])));
        $validated['speech_volume'] = max(0, min(1, floatval($settings['speech_volume'])));
        
        // Text settings
        $validated['button_text'] = sanitize_text_field($settings['button_text']) ?: 'Listen';
        
        // Color setting
        $validated['button_color'] = sanitize_hex_color($settings['button_color']) ?: '#0073aa';
        
        return $validated;
    }

    /**
     * Log security events
     *
     * @param string $event
     * @param array $data
     * @since 1.0.0
     */
    public static function log_security_event($event, $data = array()) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $log_data = array(
                'timestamp' => current_time('mysql'),
                'event' => $event,
                'user_ip' => self::get_user_ip(),
                'user_id' => get_current_user_id(),
                'data' => $data
            );
        }
    }

    /**
     * Custom die handler to prevent information disclosure
     *
     * @param callable $handler
     * @return callable
     * @since 1.0.0
     */
    public static function custom_die_handler($handler) {
        return function($message, $title = '', $args = array()) use ($handler) {
            // Don't reveal sensitive information in production
            if (!defined('WP_DEBUG') || !WP_DEBUG) {
                if (is_string($message) && strpos($message, 'map_') !== false) {
                    $message = __('An error occurred. Please try again.', MAP_TEXT_DOMAIN);
                }
            }
            
            return call_user_func($handler, $message, $title, $args);
        };
    }

    /**
     * Escape output for different contexts
     *
     * @param string $string
     * @param string $context
     * @return string
     * @since 1.0.0
     */
    public static function escape_output($string, $context = 'html') {
        switch ($context) {
            case 'attr':
                return esc_attr($string);
            case 'url':
                return esc_url($string);
            case 'js':
                return esc_js($string);
            case 'textarea':
                return esc_textarea($string);
            case 'html':
            default:
                return esc_html($string);
        }
    }

    /**
     * Generate secure random token
     *
     * @param int $length
     * @return string
     * @since 1.0.0
     */
    public static function generate_token($length = 32) {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        } elseif (function_exists('openssl_random_pseudo_bytes')) {
            return bin2hex(openssl_random_pseudo_bytes($length / 2));
        } else {
            // Fallback for older PHP versions
            return substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', $length)), 0, $length);
        }
    }

    /**
     * Check if request is from allowed origin
     *
     * @return bool
     * @since 1.0.0
     */
    public static function check_origin() {
        $allowed_origins = array(
            home_url(),
            admin_url()
        );
        
        $origin = $_SERVER['HTTP_ORIGIN'] ?? $_SERVER['HTTP_REFERER'] ?? '';
        
        if (empty($origin)) {
            return true; // Allow requests without origin (direct access)
        }
        
        foreach ($allowed_origins as $allowed) {
            if (strpos($origin, $allowed) === 0) {
                return true;
            }
        }
        
        return false;
    }
}
