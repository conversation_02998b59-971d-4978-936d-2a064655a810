<?php
/**
 * Plugin Name: My Accessibility Plugin
 * Plugin URI: https://themeforest.net/user/yourusername
 * Description: A premium WordPress accessibility plugin that helps website visitors with text-to-speech, contrast adjustments, font customization, and more accessibility features.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: my-accessibility-plugin
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 8.0
 * Network: false
 *
 * @package MyAccessibilityPlugin
 * @version 1.0.0
 * <AUTHOR> Name
 * @copyright Copyright (c) 2024, Your Name
 * @license GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('MAP_VERSION', '1.0.0');
define('MAP_PLUGIN_FILE', __FILE__);
define('MAP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MAP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MAP_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('MAP_TEXT_DOMAIN', 'my-accessibility-plugin');

/**
 * Main plugin class
 *
 * @since 1.0.0
 */
final class MyAccessibilityPlugin {

    /**
     * Plugin instance
     *
     * @var MyAccessibilityPlugin
     * @since 1.0.0
     */
    private static $instance = null;

    /**
     * Admin instance
     *
     * @var MAP_Admin
     * @since 1.0.0
     */
    public $admin;

    /**
     * Public instance
     *
     * @var MAP_Public
     * @since 1.0.0
     */
    public $public;

    /**
     * Get plugin instance
     *
     * @return MyAccessibilityPlugin
     * @since 1.0.0
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    private function __construct() {
        $this->init();
    }

    /**
     * Initialize the plugin
     *
     * @since 1.0.0
     */
    private function init() {
        // Load plugin text domain
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Initialize plugin components
        add_action('init', array($this, 'init_components'));
        
        // Plugin activation and deactivation hooks
        register_activation_hook(MAP_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(MAP_PLUGIN_FILE, array($this, 'deactivate'));
        
        // Plugin loaded hook
        add_action('plugins_loaded', array($this, 'plugins_loaded'));
    }

    /**
     * Load plugin text domain for translations
     *
     * @since 1.0.0
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            MAP_TEXT_DOMAIN,
            false,
            dirname(MAP_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Initialize plugin components
     *
     * @since 1.0.0
     */
    public function init_components() {
        // Load required files
        $this->load_dependencies();

        // Initialize admin components
        if (is_admin() && class_exists('MAP_Admin')) {
            $this->admin = new MAP_Admin();
        }

        // Initialize public components
        if (!is_admin() && class_exists('MAP_Public')) {
            // Ensure MAP_Core is initialized before public components
            if (class_exists('MAP_Core')) {
                MAP_Core::get_instance();
            }
            $this->public = new MAP_Public();
        }
    }

    /**
     * Load plugin dependencies
     *
     * @since 1.0.0
     */
    private function load_dependencies() {
        // Security and performance classes (always loaded)
        require_once MAP_PLUGIN_DIR . 'includes/class-map-security.php';
        require_once MAP_PLUGIN_DIR . 'includes/class-map-performance.php';

        // Core includes
        require_once MAP_PLUGIN_DIR . 'includes/class-map-core.php';
        require_once MAP_PLUGIN_DIR . 'includes/class-map-settings.php';
        require_once MAP_PLUGIN_DIR . 'includes/class-map-text-to-speech.php';

        // Admin includes
        if (is_admin()) {
            require_once MAP_PLUGIN_DIR . 'admin/class-map-admin.php';
            require_once MAP_PLUGIN_DIR . 'admin/class-map-admin-settings.php';
        }

        // Public includes
        if (!is_admin()) {
            require_once MAP_PLUGIN_DIR . 'public/class-map-public.php';
            require_once MAP_PLUGIN_DIR . 'public/class-map-frontend-widget.php';
        }

        // Initialize security and performance
        MAP_Security::init();
        MAP_Performance::init();
    }

    /**
     * Plugin activation
     *
     * @since 1.0.0
     */
    public function activate() {
        // Create default options
        $default_options = array(
            'text_to_speech_enabled' => true,
            'widget_position' => 'bottom-right',
            'widget_style' => 'modern',
            'speech_rate' => 1.0,
            'speech_pitch' => 1.0,
            'speech_volume' => 1.0
        );
        
        add_option('map_settings', $default_options);
        
        // Create database tables if needed (for future features)
        $this->create_tables();
        
        // Set plugin version
        add_option('map_version', MAP_VERSION);
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     *
     * @since 1.0.0
     */
    public function deactivate() {
        // Clean up temporary data
        delete_transient('map_cache');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Create database tables
     *
     * @since 1.0.0
     */
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Future: Create tables for analytics, user preferences, etc.
        // For now, we'll use WordPress options
    }

    /**
     * Plugin loaded hook
     *
     * @since 1.0.0
     */
    public function plugins_loaded() {
        // Check for plugin updates
        $this->check_version();

        // Initialize core functionality only if classes are loaded
        if (class_exists('MAP_Core')) {
            MAP_Core::get_instance();
        }
    }

    /**
     * Check plugin version and run updates if needed
     *
     * @since 1.0.0
     */
    private function check_version() {
        $current_version = get_option('map_version', '0.0.0');
        
        if (version_compare($current_version, MAP_VERSION, '<')) {
            // Run update procedures
            $this->update_plugin($current_version);
            update_option('map_version', MAP_VERSION);
        }
    }

    /**
     * Update plugin
     *
     * @param string $from_version
     * @since 1.0.0
     */
    private function update_plugin($from_version) {
        // Update button text for existing installations
        $settings = get_option('map_settings', array());
        if (isset($settings['button_text']) && $settings['button_text'] === 'Listen') {
            $settings['button_text'] = 'Accessibility';
            update_option('map_settings', $settings);
        }

        // Force refresh settings to ensure new defaults are applied
        if (class_exists('MAP_Settings')) {
            $settings_instance = new MAP_Settings();
            $current_settings = $settings_instance->get_settings();
            $current_settings['button_text'] = 'Accessibility';
            $settings_instance->update_settings($current_settings);
        }
    }

    /**
     * Prevent cloning
     *
     * @since 1.0.0
     */
    private function __clone() {}

    /**
     * Prevent unserialization
     *
     * @since 1.0.0
     */
    public function __wakeup() {}
}

/**
 * Initialize the plugin
 *
 * @return MyAccessibilityPlugin
 * @since 1.0.0
 */
function MAP() {
    return MyAccessibilityPlugin::get_instance();
}

// Initialize the plugin
MAP();
