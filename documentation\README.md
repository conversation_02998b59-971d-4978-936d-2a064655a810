# My Accessibility Plugin Documentation

Welcome to the comprehensive documentation for My Accessibility Plugin - a premium WordPress accessibility solution designed to make your website more inclusive and accessible to all users.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Features](#features)
5. [Customization](#customization)
6. [Troubleshooting](#troubleshooting)
7. [Developer Guide](#developer-guide)
8. [Support](#support)

## Getting Started

My Accessibility Plugin helps make your WordPress website more accessible by providing essential tools for users with disabilities. The plugin focuses on text-to-speech functionality with plans for additional accessibility features in future versions.

### System Requirements

- WordPress 5.0 or higher
- PHP 8.0 or higher
- Modern web browser with JavaScript enabled
- Web Speech API support (for text-to-speech)

### Browser Compatibility

The plugin works with all modern browsers:
- ✅ Chrome 33+
- ✅ Firefox 49+
- ✅ Safari 14.1+
- ✅ Edge 14+
- ⚠️ Internet Explorer (limited support)

## Installation

### Automatic Installation (Recommended)

1. Log in to your WordPress admin dashboard
2. Navigate to **Plugins > Add New**
3. Search for "My Accessibility Plugin"
4. Click **Install Now** and then **Activate**

### Manual Installation

1. Download the plugin zip file from ThemeForest
2. Log in to your WordPress admin dashboard
3. Navigate to **Plugins > Add New > Upload Plugin**
4. Choose the downloaded zip file and click **Install Now**
5. Click **Activate Plugin** after installation completes

### Verification

After installation, you should see:
- A new "Accessibility" menu item under **Settings**
- An accessibility widget on your website frontend
- A dashboard widget showing plugin status

## Configuration

### Basic Setup

1. Go to **Settings > Accessibility** in your WordPress admin
2. Configure the basic settings:
   - ✅ Enable Text-to-Speech
   - Choose widget position (bottom-right recommended)
   - Select widget style (modern, classic, or minimal)

### Speech Settings

Configure how the text-to-speech feature behaves:

- **Speech Rate**: Controls reading speed (0.1 - 10.0)
  - 0.5 = Very slow
  - 1.0 = Normal speed (default)
  - 2.0 = Fast
- **Speech Pitch**: Controls voice pitch (0.0 - 2.0)
- **Speech Volume**: Controls volume level (0.0 - 1.0)

### Widget Customization

Customize the appearance of the accessibility widget:

- **Position**: Choose from 5 positions (top-left, top-right, bottom-left, bottom-right, center)
- **Style**: Select visual style (modern, classic, minimal)
- **Button Text**: Customize the button label (default: "Listen")
- **Button Color**: Choose your brand color
- **Button Size**: Small, medium, or large

### Advanced Options

- **Auto Highlight**: Highlight text while reading (recommended)
- **Keyboard Shortcuts**: Enable keyboard navigation (recommended)

## Features

### Text-to-Speech

The core feature that reads website content aloud:

**How it works:**
1. User clicks the accessibility button
2. Plugin extracts text from the main content area
3. Browser's Web Speech API converts text to speech
4. User can control playback with play/pause/stop buttons

**User Controls:**
- Play/Pause button
- Stop button
- Speed adjustment slider
- Progress indicator

**Keyboard Shortcuts:**
- `Alt + A`: Open accessibility tools
- `Alt + S`: Start/pause reading
- `Alt + X`: Stop reading
- `Esc`: Close accessibility panel

### Accessibility Widget

A floating widget that provides easy access to accessibility tools:

**Features:**
- Responsive design that works on all devices
- High contrast support
- Keyboard navigation
- Screen reader compatible
- WCAG 2.1 compliant

### Admin Interface

Comprehensive admin panel for configuration:

**Dashboard Widget:**
- Quick status overview
- Direct links to settings and frontend
- Current configuration summary

**Settings Page:**
- Organized into logical sections
- Real-time validation
- Import/Export functionality
- Preview options

## Customization

### CSS Customization

The plugin uses CSS custom properties for easy theming:

```css
:root {
    --map-primary-color: #0073aa;
    --map-primary-hover: #005a87;
    --map-text-color: #ffffff;
    --map-bg-color: #ffffff;
    --map-border-radius: 8px;
}
```

### Hooks and Filters

For developers who want to extend the plugin:

**Filters:**
- `map_should_load_accessibility`: Control when to load accessibility features
- `map_process_speech_text`: Modify text before speech synthesis
- `map_page_content`: Customize content extraction
- `map_page_accessibility_data`: Modify page data for accessibility processing

**Actions:**
- `map_before_widget_render`: Before widget HTML output
- `map_after_widget_render`: After widget HTML output
- `map_settings_updated`: When settings are saved

### Widget Positioning

Custom positioning with CSS:

```css
.map-accessibility-widget.custom-position {
    top: 50px;
    right: 20px;
    /* Your custom positioning */
}
```

## Troubleshooting

### Common Issues

**Text-to-Speech Not Working:**
- Check browser compatibility (Web Speech API required)
- Ensure JavaScript is enabled
- Try a different browser
- Check browser console for errors

**Widget Not Appearing:**
- Verify plugin is activated
- Check if text-to-speech is enabled in settings
- Look for JavaScript errors in browser console
- Test with default theme to rule out theme conflicts

**Performance Issues:**
- Plugin is optimized and shouldn't cause slowdowns
- Check for conflicts with other plugins
- Ensure you're using the latest version

**Styling Issues:**
- Check for CSS conflicts with your theme
- Use browser developer tools to inspect elements
- Try different widget positions and styles

### Debug Mode

Enable WordPress debug mode to troubleshoot issues:

```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Getting Help

If you need assistance:

1. Check this documentation first
2. Search our support forum
3. Contact our support team
4. Provide detailed information about your issue:
   - WordPress version
   - Plugin version
   - Browser and version
   - Steps to reproduce the issue
   - Any error messages

## Developer Guide

### Plugin Architecture

The plugin follows WordPress best practices:

```
my-accessibility-plugin/
├── my-accessibility-plugin.php (Main plugin file)
├── includes/ (Core functionality)
├── admin/ (Admin interface)
├── public/ (Frontend functionality)
├── assets/ (CSS, JS, images)
└── documentation/ (This documentation)
```

### Class Structure

- `MyAccessibilityPlugin`: Main plugin class
- `MAP_Core`: Core functionality
- `MAP_Settings`: Settings management
- `MAP_Text_To_Speech`: Speech synthesis
- `MAP_Admin`: Admin interface
- `MAP_Public`: Frontend functionality

### Extending the Plugin

Create custom functionality:

```php
// Add custom text processing
add_filter('map_process_speech_text', function($text) {
    // Your custom processing
    return $text;
});

// Modify widget behavior
add_action('map_before_widget_render', function() {
    // Your custom code
});
```

## Support

### Documentation

- **User Guide**: Complete user documentation
- **Developer Guide**: Technical documentation for developers
- **FAQ**: Frequently asked questions
- **Troubleshooting**: Common issues and solutions

### Getting Help

- **Support Forum**: Community support and discussions
- **Premium Support**: Direct support for premium customers
- **Bug Reports**: Report issues and bugs
- **Feature Requests**: Suggest new features

### Stay Updated

- Follow our blog for updates and tips
- Subscribe to our newsletter
- Join our community forum
- Follow us on social media

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Author**: Your Name  
**License**: GPL v2 or later
