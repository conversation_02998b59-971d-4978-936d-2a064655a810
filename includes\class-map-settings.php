<?php
/**
 * Settings management class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Settings class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Settings {

    /**
     * Settings option name
     *
     * @var string
     * @since 1.0.0
     */
    private $option_name = 'map_settings';

    /**
     * Default settings
     *
     * @var array
     * @since 1.0.0
     */
    private $default_settings = array(
        'text_to_speech_enabled' => true,
        'widget_position' => 'bottom-right',
        'widget_style' => 'modern',
        'speech_rate' => 1.0,
        'speech_pitch' => 1.0,
        'speech_volume' => 1.0,
        'button_text' => 'Accessibility',
        'button_color' => '#0073aa',
        'button_size' => 'medium',
        'auto_highlight' => true,
        'keyboard_shortcuts' => true
    );

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        // Initialize settings if they don't exist
        if (false === get_option($this->option_name)) {
            add_option($this->option_name, $this->default_settings);
        }
    }

    /**
     * Get all settings
     *
     * @return array
     * @since 1.0.0
     */
    public function get_settings() {
        $settings = get_option($this->option_name, $this->default_settings);
        return wp_parse_args($settings, $this->default_settings);
    }

    /**
     * Get a specific setting
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     * @since 1.0.0
     */
    public function get_setting($key, $default = null) {
        $settings = $this->get_settings();
        
        if (isset($settings[$key])) {
            return $settings[$key];
        }
        
        if (null !== $default) {
            return $default;
        }
        
        return isset($this->default_settings[$key]) ? $this->default_settings[$key] : null;
    }

    /**
     * Update settings
     *
     * @param array $new_settings
     * @return bool
     * @since 1.0.0
     */
    public function update_settings($new_settings) {
        $current_settings = $this->get_settings();
        $updated_settings = wp_parse_args($new_settings, $current_settings);
        
        // Validate settings
        $validated_settings = $this->validate_settings($updated_settings);
        
        return update_option($this->option_name, $validated_settings);
    }

    /**
     * Update a specific setting
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     * @since 1.0.0
     */
    public function update_setting($key, $value) {
        $settings = $this->get_settings();
        $settings[$key] = $value;
        
        return $this->update_settings($settings);
    }

    /**
     * Reset settings to defaults
     *
     * @return bool
     * @since 1.0.0
     */
    public function reset_settings() {
        return update_option($this->option_name, $this->default_settings);
    }

    /**
     * Delete all settings
     *
     * @return bool
     * @since 1.0.0
     */
    public function delete_settings() {
        return delete_option($this->option_name);
    }

    /**
     * Validate settings
     *
     * @param array $settings
     * @return array
     * @since 1.0.0
     */
    public function validate_settings($settings) {
        $validated = array();
        
        // Text to speech enabled
        $validated['text_to_speech_enabled'] = isset($settings['text_to_speech_enabled']) ? 
            (bool) $settings['text_to_speech_enabled'] : $this->default_settings['text_to_speech_enabled'];
        
        // Widget position
        $valid_positions = array('top-left', 'top-right', 'bottom-left', 'bottom-right', 'center');
        $validated['widget_position'] = in_array($settings['widget_position'], $valid_positions) ? 
            $settings['widget_position'] : $this->default_settings['widget_position'];
        
        // Widget style
        $valid_styles = array('modern', 'classic', 'minimal');
        $validated['widget_style'] = in_array($settings['widget_style'], $valid_styles) ? 
            $settings['widget_style'] : $this->default_settings['widget_style'];
        
        // Speech rate (0.1 to 10)
        $validated['speech_rate'] = max(0.1, min(10, floatval($settings['speech_rate'])));
        
        // Speech pitch (0 to 2)
        $validated['speech_pitch'] = max(0, min(2, floatval($settings['speech_pitch'])));
        
        // Speech volume (0 to 1)
        $validated['speech_volume'] = max(0, min(1, floatval($settings['speech_volume'])));
        
        // Button text
        $validated['button_text'] = sanitize_text_field($settings['button_text']) ?: $this->default_settings['button_text'];
        
        // Button color
        $validated['button_color'] = sanitize_hex_color($settings['button_color']) ?: $this->default_settings['button_color'];
        
        // Button size
        $valid_sizes = array('small', 'medium', 'large');
        $validated['button_size'] = in_array($settings['button_size'], $valid_sizes) ? 
            $settings['button_size'] : $this->default_settings['button_size'];
        
        // Auto highlight
        $validated['auto_highlight'] = isset($settings['auto_highlight']) ? 
            (bool) $settings['auto_highlight'] : $this->default_settings['auto_highlight'];
        
        // Keyboard shortcuts
        $validated['keyboard_shortcuts'] = isset($settings['keyboard_shortcuts']) ? 
            (bool) $settings['keyboard_shortcuts'] : $this->default_settings['keyboard_shortcuts'];
        
        return $validated;
    }

    /**
     * Get default settings
     *
     * @return array
     * @since 1.0.0
     */
    public function get_default_settings() {
        return $this->default_settings;
    }

    /**
     * Export settings
     *
     * @return string JSON encoded settings
     * @since 1.0.0
     */
    public function export_settings() {
        return json_encode($this->get_settings());
    }

    /**
     * Import settings
     *
     * @param string $json_settings
     * @return bool
     * @since 1.0.0
     */
    public function import_settings($json_settings) {
        $settings = json_decode($json_settings, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        
        return $this->update_settings($settings);
    }
}
