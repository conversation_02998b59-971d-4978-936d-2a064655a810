# My Accessibility Plugin

A modern WordPress accessibility plugin with text-to-speech functionality that activates on text selection.

## Features

- **Text-to-Speech**: Select any text on the page to hear it read aloud
- **Modern Dark UI**: Clean, professional accessibility panel design
- **Easy Toggle**: Simple on/off switch for accessibility features
- **Keyboard Shortcuts**: Alt+A to open panel, Alt+S to toggle speech, Alt+X to stop
- **Responsive Design**: Works on all device sizes
- **Security**: Built with WordPress security best practices

## Installation

1. **Download** the plugin files
2. **Create a ZIP** of the entire `my-accessibility-plugin` folder
3. **Upload** via WordPress Admin → Plugins → Add New → Upload Plugin
4. **Activate** the plugin
5. **Configure** via Settings → Accessibility

## How to Use

### Text-to-Speech Feature

1. **Click the "Accessibility" button** (bottom-right corner by default)
2. **Toggle "Text to Speech" ON** in the panel
3. **Select any text** on your website
4. **The text will be read aloud automatically**

### Keyboard Shortcuts

- `Alt + A`: Open/close accessibility panel
- `Alt + S`: Toggle text-to-speech on/off
- `Alt + X`: Stop current speech
- `Escape`: Close accessibility panel

## Configuration

Go to **Settings → Accessibility** in your WordPress admin to configure:

- Enable/disable the plugin
- Change button position (top-left, top-right, bottom-left, bottom-right)
- Customize button text
- Adjust speech settings

## Browser Compatibility

- ✅ Chrome 33+
- ✅ Firefox 49+
- ✅ Safari 7+
- ✅ Edge 14+
- ❌ Internet Explorer (not supported)

## Testing

1. **Activate the plugin**
2. **Visit your website frontend**
3. **Look for the "Accessibility" button** (usually bottom-right)
4. **Click it to open the panel**
5. **Toggle "Text to Speech" ON**
6. **Select some text** on the page
7. **Verify it reads the text aloud**

## Troubleshooting

### Plugin Won't Activate
- Check PHP version (requires 8.0+)
- Check WordPress version (requires 5.0+)
- Run the debug script: `/wp-content/plugins/my-accessibility-plugin/debug-activation.php`

### Text-to-Speech Not Working
- Ensure your browser supports Speech Synthesis API
- Check browser permissions for audio
- Try refreshing the page
- Check browser console for JavaScript errors

### Button Not Appearing
- Check if plugin is enabled in Settings → Accessibility
- Clear any caching plugins
- Check for JavaScript conflicts with other plugins

## File Structure

```
my-accessibility-plugin/
├── my-accessibility-plugin.php     # Main plugin file
├── includes/                       # Core functionality
│   ├── class-map-core.php
│   ├── class-map-settings.php
│   ├── class-map-text-to-speech.php
│   ├── class-map-security.php
│   └── class-map-performance.php
├── admin/                          # Admin interface
│   ├── class-map-admin.php
│   └── class-map-admin-settings.php
├── public/                         # Frontend functionality
│   ├── class-map-public.php
│   └── class-map-frontend-widget.php
├── assets/                         # CSS and JavaScript
│   ├── css/
│   │   ├── frontend.css
│   │   └── admin.css
│   └── js/
│       ├── frontend.js
│       └── admin.js
└── languages/                      # Translation files
```

## Development

### Requirements
- PHP 8.0+
- WordPress 5.0+
- Modern browser with Speech Synthesis API support

### Security Features
- Nonce verification for all AJAX requests
- Input sanitization and validation
- Capability checks for admin functions
- Directory browsing protection

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Use the debug script for detailed diagnostics
3. Check browser console for JavaScript errors
4. Verify all requirements are met

## License

This plugin follows WordPress coding standards and GPL licensing.
